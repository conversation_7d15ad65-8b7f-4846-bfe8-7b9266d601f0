@work-meta-data: .work-meta-data;

@{work-meta-data} {
  display: flex;
  flex: 1;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
  margin: 0 8px 8px 0;

  .left-divider-content-r {
    display: flex;
  }
  // 左侧 Tree
  .meta-data-left-tree {
    height: 100%;
    padding: 8px 16px;
    border-right: none;

    &-header {
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      align-items: center;
      margin-bottom: 5px;

      &-operations {
        & > button {
          margin-inline: 5px;
        }
        button {
          padding-left: 12px;
          padding-right: 0px;
          width: 20px;
          height: 20px;

          &:hover {
            background-color: #f0f0f1;
          }
        }

        .acud-btn-only-icon {
          padding-right: 6px;
          padding-left: 6px;
        }

        .acud-dropdown-trigger {
          display: inline-block;
        }
      }

      > h3 {
        font-size: 14px;
        font-weight: 500;
      }
    }

    &-content {
      height: calc(100% - 40px);
      overflow: auto;
      margin-top: 10px;
    }

    .acud-tree {
      .acud-tree-treenode {
        overflow: hidden;
      }

      .acud-tree-node-content-wrapper {
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }

  // 右侧 Content
  .meta-data-right-content {
    padding: 14px 16px;
    height: 100%;
    overflow: auto;
    flex: 1;

    .description-edit-com {
      margin-bottom: 15px;
    }
  }

  .work-meta-catalog-panel,
  .work-meta-scheam-panel,
  .work-meta-table-panel,
  .work-meta-volume-panel,
  .work-meta-operator-panel {
    .title-head {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 10px;
      .list-size {
        color: rgba(92, 95, 102, 1);
        font-size: 12px;
        font-weight: 400;
      }
    }
  }

  // Catalog Panel 面板
  .work-meta-catalog-panel {
    // 无权限panel
    .empty-panel {
      width: 100%;
      height: calc(100vh - 200px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &-img {
        width: 100px;
        height: 100px;
        margin-bottom: 24px;
      }

      &-title {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        color: #000;
        margin-bottom: 8px;
      }

      &-text {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #5c5f66;
      }
    }
  }

  // Scheam Panel 面板
  .work-meta-scheam-panel {
  }

  // Table Panel 面板
  .work-meta-table-panel {
    .table-overview-search {
      width: 260px;
      margin: 15px 0;
    }
  }

  // Volume Panel 面板
  .work-meta-volume-panel {
    .volume-file-table {
      padding-bottom: 30px;

      .table-file-name {
        display: inline-flex;
        align-items: center;
        height: 20px;
        width: 100%;
        overflow: hidden;

        &-icon {
          margin-right: 4px;
        }
      }

      .acud-table {
        .acud-table-tbody {
          & > tr {
            & > td {
              padding: 2px 12px;
            }
          }
        }
        .acud-table-tbody > tr > td.acud-table-selection-column {
          padding-right: 0;
        }
      }

      &-head {
        margin: 10px 0 15px;
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
      }

      &-action {
        word-break: break-all;

        .clip-board-container {
          cursor: pointer;
          display: inline;
          margin-left: 10px;
        }

        .splitting-symbol {
          font: 500;
          color: #ccc;
        }
      }

      &-default-path {
        margin-left: 15px;
      }

      .file-table-action {
        > button {
          min-width: 0;
          padding-left: 0;
        }
      }

      .clipboard-inline-btn {
        display: inline-block;
      }

      &-patination {
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;

        ul.acud-pagination {
          width: auto;
          margin-right: 10px;

          > li:not(.acud-pagination-options) {
            display: none;
          }
        }
      }

      &-hasPage-patination {
        margin-top: 10px;
        display: flex;
        justify-content: flex-end;
        ul.acud-pagination {
          width: auto;
          margin-right: 10px;
        }
      }
    }
  }
  .meta-data-right-content {
    .work-meta-operator-panel {
      &-edit {
        padding: 0 0 8px;
        margin-bottom: 0;
      }
    }
  }

  // Operator Panel 面板
  .work-meta-operator-panel {
    &-usage {
      background-color: #f7f7f9;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 24px;

      .title {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #151b26;
        margin-bottom: 8px;
      }

      .content {
        white-space: pre-wrap;
      }
    }

    .version-name-btn {
      padding: 0;
    }

    &-pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;

      .acud-pagination {
        width: fit-content;
      }
    }
  }
}

// Volume 上传弹窗
.meta-volume-upload-box {
  display: flex;
  position: relative;
  height: 260px;
  margin-bottom: 15px;

  > span {
    flex: 1;
  }

  div.acud-upload-select {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 100%;
    height: 100%;
    cursor: pointer;

    &:hover {
      border-color: #528eff;
    }
  }

  .drag-content {
    padding: 40px 0;
    text-align: center;
    height: 100%;
    font-size: 14px;
    line-height: 39px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;

    .volume-upload-des {
      color: #bebdbd;
      text-align: left;
      line-height: 20px;
    }

    .volume-upload-text {
      > button {
        font-size: 14px;
        margin-top: -3px;
        padding: 0;
      }

      &-extra {
        position: absolute;
        bottom: 0;
      }
    }
  }

  &-dir {
    .acud-upload-list {
      pointer-events: none;
    }
  }

  .acud-upload-span {
    display: flex;
    align-items: center;
    background: #f6f4f4;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 10px;
  }

  .acud-upload-list {
    margin: 0;
    padding: 0;
    line-height: 1.6667;
    position: absolute;
    top: 14px;
    left: 12px;
    width: ~'calc(100% - 24px)';
    max-height: 206px;
    overflow-y: auto;
    background: #fff;

    .acud-upload-list-text-container:first-child {
      margin-top: 0;
    }

    .acud-upload-list-text-container {
      margin-bottom: 10px;
      .acud-upload-list-item {
        background: #f7f7f9;
        border-radius: 6px;
      }
      .acud-upload-list-item:hover {
        background-color: inherit;
      }
    }

    .acud-upload-list-item-info {
      height: 32px;
      .acud-upload-span {
        padding-right: 12px;
        border: none;
        border-radius: 6px;
      }
    }

    .acud-upload-list-item-card-actions {
      right: 14px;
    }

    .acud-upload-list-item-card-actions-btn,
    .acudicon-outlined-close {
      opacity: 1;
    }

    .acud-upload-list-item .acudicon-outlined-close {
      opacity: 1;
    }
    &.acud-upload-list-text .acud-upload-list-item-name {
      padding: 0 16px 0 8px;
    }
  }
}

.meta-volume-upload-form.acud-form-horizontal {
  .acud-form-item-label {
    padding-left: 0;
    color: red !important;
  }
}

.meta-data-right-content-loading-wrapper {
  width: 100%;
  height: 100%;
}

.work-meta-version-drawer {
  .info-panel-show-com-label {
    font-size: 12px;
  }
}

.meta-data-left-tree-header-operations-setting-menu,
.meta-data-left-tree-header-operations-add-menu {
  .acud-divider {
    margin: 6px;
    width: calc(100% - 12px);
    min-width: unset;
  }

  .acud-dropdown-menu-item {
    padding-inline: 6px !important;
  }
}

.meta-data-left-tree-header-operations-add-menu {
  width: 166px;
}

/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-06-11 19:28:06
 * @LastEditTime: 2025-06-24 13:24:43
 * @FilePath: /console-databuilder/src/pages/MetaData/config.ts
 */
import {CatalogType} from '@api/metaRequest';
import {RULE} from '@utils/regs';
import {Privilege} from '@api/permission/type';

export const MetaCnNameMap = {
  Catalog: '数据目录',
  Schema: '数据模式',
  Table: '数据表',
  Volume: '数据卷',
  Operator: '算子',
  OperatorVersion: '算子版本',
  Dataset: '数据集',
  Model: '模型'
};

// 根据目录类型区分命名正则表达式
export const ruleMapByCatalog = {
  [CatalogType.DATALAKE]: {
    rule: RULE.specialNameStartEn64,
    text: RULE.specialNameStartEn64Text
  },
  [CatalogType.DORIS]: {
    rule: RULE.specialNameStartEn64,
    text: RULE.specialNameStartEn64Text
  }
};

// 模型和数据集 过滤类型
export enum DatasetAndModelFieldEnum {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  AUDIO = 'AUDIO',
  VIDEO = 'VIDEO',
  GENERAL = 'GENERAL'
}
// 模型和数据集 过滤类型 中文
export const DatasetAndModelFieldChineseMap = {
  [DatasetAndModelFieldEnum.TEXT]: '文本',
  [DatasetAndModelFieldEnum.IMAGE]: '图片',
  [DatasetAndModelFieldEnum.AUDIO]: '音频',
  [DatasetAndModelFieldEnum.VIDEO]: '视频'
  // [DatasetAndModelFieldEnum.GENERAL]: '通用'
};

// 数据集解析文件格式
export const DatasetAndModelParseFiles = {
  [DatasetAndModelFieldEnum.TEXT]: [
    {label: 'Parquet', value: 'Parquet'},
    {label: 'CSV', value: 'CSV'},
    {label: 'JSON', value: 'JSON'},
    {label: 'Text', value: 'Text'}
  ],
  [DatasetAndModelFieldEnum.IMAGE]: [
    {label: 'png', value: 'png'},
    {label: 'jpg', value: 'jpg'},
    {label: 'bmp', value: 'bmp'},
    {label: 'jpeg', value: 'jpeg'}
  ],
  [DatasetAndModelFieldEnum.AUDIO]: [
    {label: 'wav', value: 'wav'},
    {label: 'mp3', value: 'mp3'},
    {label: 'flac', value: 'flac'},
    {label: 'ogg', value: 'ogg'}
  ],
  [DatasetAndModelFieldEnum.VIDEO]: [
    {label: 'mp4', value: 'mp4'},
    {label: 'avi', value: 'avi'}
  ]
};

export const UploadFileLimit = {
  [DatasetAndModelFieldEnum.TEXT]: ['text/plain', 'application/json', 'application/x-jsonlines'],
  [DatasetAndModelFieldEnum.IMAGE]: ['image/png', 'image/jpeg', 'image/bmp'],
  [DatasetAndModelFieldEnum.AUDIO]: ['audio/flac', 'audio/mpeg', 'audio/wav', 'audio/ogg'],
  [DatasetAndModelFieldEnum.GENERAL]: ['video/mp4', 'video/avi']
};

// TODO 支持的基础模型类型：暂时先这么写，后边可考虑替换为接口获取
export const ModelBaseModel = [
  {
    label: 'Llama2',
    value: 'Llama2',
    children: [
      {
        label: 'Llama2-7B',
        value: 'Llama2-7B'
      },
      {
        label: 'Llama2-13B',
        value: 'Llama2-13B'
      }
    ]
  },
  {
    label: 'Llam3.1',
    value: 'Llam3.1',
    children: [
      {
        label: 'Llama3.1-8B',
        value: 'Llama3.1-8B'
      },
      {
        label: 'Llama3.1-70B',
        value: 'Llama3.1-70B'
      }
    ]
  },
  {
    label: 'Qwen2',
    value: 'Qwen2',
    children: [
      {
        label: 'Qwen2-0.5B',
        value: 'Qwen2-0.5B'
      },
      {
        label: 'Qwen2-1.5B',
        value: 'Qwen2-1.5B'
      },
      {
        label: 'Qwen2-7B',
        value: 'Qwen2-7B'
      },
      {
        label: 'Qwen2-72B',
        value: 'Qwen2-72B'
      },
      {
        label: 'Qwen2-57B-A14B',
        value: 'Qwen2-57B-A14B'
      },
      {
        label: 'Qwen2-VL-7B',
        value: 'Qwen2-VL-7B'
      },
      {
        label: 'Qwen2-VL-72B',
        value: 'Qwen2-VL-72B'
      }
    ]
  },
  {
    label: 'Qwen2.5',
    value: 'Qwen2.5',
    children: [
      {
        label: 'Qwen2.5-0.5B',
        value: 'Qwen2.5-0.5B'
      },
      {
        label: 'Qwen2.5-1.5B',
        value: 'Qwen2.5-1.5B'
      },
      {
        label: 'Qwen2.5-3B',
        value: 'Qwen2.5-3B'
      },
      {
        label: 'Qwen2.5-7B',
        value: 'Qwen2.5-7B'
      },
      {
        label: 'Qwen2.5-14B',
        value: 'Qwen2.5-14B'
      },
      {
        label: 'Qwen2.5-72B',
        value: 'Qwen2.5-72B'
      }
    ]
  },
  {
    label: 'Baichuan2',
    value: 'Baichuan2',
    children: [
      {
        label: 'Baichuan2-7B',
        value: 'Baichuan2-7B'
      },
      {
        label: 'Baichuan2-13B',
        value: 'Baichuan2-13B'
      }
    ]
  },
  {
    label: 'Mixtral',
    value: 'Mixtral',
    children: [
      {
        label: 'Mixtral-7B*8',
        value: 'Mixtral-7B*8'
      },
      {
        label: 'Mixtral-22B*8',
        value: 'Mixtral-22B*8'
      }
    ]
  },
  {
    label: 'DeepSeek',
    value: 'DeepSeek',
    children: [
      {
        label: 'DeepSeek-V3-0324',
        value: 'DeepSeek-V3-0324'
      },
      {
        label: 'DeepSeek-V3',
        value: 'DeepSeek-V3'
      },
      {
        label: 'DeepSeek-R1',
        value: 'DeepSeek-R1'
      },
      {
        label: 'DeepSeek-R1-0528',
        value: 'DeepSeek-R1-0528'
      },
      {
        label: 'DeepSeek-R1-Distill-Qwen-32B',
        value: 'DeepSeek-R1-Distill-Qwen-32B'
      },
      {
        label: 'DeepSeek-R1-Distill-Qwen-14B',
        value: 'DeepSeek-R1-Distill-Qwen-14B'
      },
      {
        label: 'DeepSeek-R1-Distill-Qwen-7B',
        value: 'DeepSeek-R1-Distill-Qwen-7B'
      },
      {
        label: 'DeepSeek-R1-Distill-Qwen-1.5B',
        value: 'DeepSeek-R1-Distill-Qwen-1.5B'
      }
    ]
  }
];

// 侧边栏设置按钮下拉菜单项
export const LeftTreeSettingMenuItems = [
  {
    key: 'connectionManagement',
    label: '数据源管理',
    iconType: ''
  }
];

// 侧边栏新增按钮下拉菜单项
export const LeftTreeAddMenuItems = [
  {
    key: 'dataIntegration',
    label: '数据集成',
    iconType: 'open',
    authName: Privilege.IntegrationMenu
  },
  {
    key: 'upload',
    label: '上传文件到数据卷'
  },
  {
    key: 'dividerFirst'
  },
  {
    key: 'createCatalog',
    label: '创建数据目录',
    authName: Privilege.CreateCatalog
  },
  {
    key: 'createConnection',
    label: '创建数据源',
    authName: Privilege.CreateConnection
  }
];

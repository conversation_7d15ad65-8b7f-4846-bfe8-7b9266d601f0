import {FC, useState} from 'react';
import {Form, Input, Modal} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {editOperator} from '@api/meta/operate';

interface IEditOperatorModalProps {
  fullName: string;
  alias: string;
  workspaceId: string;
  visible: boolean;
  handleCloseModal: () => void;
  successCallback: (name?: string) => void;
}

const EditOperatorModal: FC<IEditOperatorModalProps> = ({
  fullName,
  alias,
  workspaceId,
  visible,
  handleCloseModal,
  successCallback
}) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleConfirm = useMemoizedFn(() => {
    form.validateFields().then(async (values) => {
      try {
        setLoading(true);
        const res = await editOperator(workspaceId, fullName, values);
        successCallback();
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    });
    handleCloseModal();
  });

  const onCloseModal = () => {
    handleCloseModal();
  };

  return (
    <Modal
      closable={true}
      title={'创建算子'}
      width={500}
      visible={visible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{loading}}
      destroyOnClose={true}
    >
      <Form labelAlign="left" layout="vertical" colon={false} labelWidth={80} form={form}>
        <Form.Item label="原算子别称" name="oldAlias" initialValue={alias}>
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="新算子别称"
          name="alias"
          validateFirst
          rules={[{required: true, message: '请输入算子名称'}]} // 校验特殊字符和长度限制
        >
          <Input
            placeholder="请输入算子别名，用于工作流中显示"
            allowClear
            limitLength={64}
            forbidIfLimit={true}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditOperatorModal;

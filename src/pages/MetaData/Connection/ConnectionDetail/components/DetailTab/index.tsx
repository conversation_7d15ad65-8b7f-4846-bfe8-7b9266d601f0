/**
 * @file 数据源详情 - 详情tab页
 * <AUTHOR>
 */

import {IConnection} from '@api/connection';
import {FileSourceType} from '@api/integration/type';
import {AuthenticationTypeMap} from '@pages/MetaData/Connection/constants';
import {FC} from 'react';
import styles from './index.module.less';

const PREFIX = 'connection-detail-tab';

interface IConnectionDetailTabProps {
  connectionDetail?: IConnection;
}

const ConnectionDetailTab: FC<IConnectionDetailTabProps> = ({connectionDetail}) => {
  // 渲染详情项
  const getRender = () => {
    const {type, host, port, defaultFs, hdfsSitePath, authenticationType} = connectionDetail || {};

    if (type === FileSourceType.HDFS) {
      return [
        {
          label: '类型',
          key: 'type',
          render: <span>{type || '-'}</span>
        },
        {
          label: 'defaultFS',
          key: 'defaultFs',
          render: <span>{defaultFs || '-'}</span>
        },
        {
          label: 'hdfs_site_path',
          key: 'hdfsSitePath',
          render: <span>{hdfsSitePath || '-'}</span>
        },
        {
          label: '认证方式',
          key: 'authenticationType',
          render: <span>{AuthenticationTypeMap[authenticationType] || '-'}</span>
        }
      ];
    }
    return [
      {
        label: '类型',
        key: 'type',
        render: <span>{type || '-'}</span>
      },
      {
        label: '主机',
        key: 'metastore',
        render: <span>{host || '-'}</span>
      },
      {
        label: '端口',
        key: 'creator',
        render: <span>{port || '-'}</span>
      }
    ];
  };

  return (
    <div className={styles[PREFIX]}>
      <div className={styles[`${PREFIX}-comment`]}>
        <div className={styles[`${PREFIX}-comment-label`]}>描述</div>
        <div className={styles[`${PREFIX}-comment-content`]}>{connectionDetail?.comment || '-'}</div>
      </div>
      <div className={styles[`${PREFIX}-info`]}>
        <div className={styles[`${PREFIX}-info-label`]}>
          <span className={styles[`${PREFIX}-info-label-title`]}>基本信息</span>
          <span>共3条</span>
        </div>
        <div className={styles[`${PREFIX}-info-table`]}>
          {getRender().map((item) => (
            <div className={styles[`${PREFIX}-info-table-item`]} key={item.key}>
              <div className={styles[`${PREFIX}-info-table-item-label`]}>{item.label}</div>
              <div className={styles[`${PREFIX}-info-table-item-content`]}>{item.render}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ConnectionDetailTab;

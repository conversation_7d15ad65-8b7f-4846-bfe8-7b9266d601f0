/**
 * @file 连通测试
 * <AUTHOR>
 */

import flags from '@/flags';
import {ComputeResourceItem, Engine, getComputeResourceList, TASK_INSTANCE_STATUS} from '@api/Compute';
import {testConnection} from '@api/connection';
import {Ellipsis, Status} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import {Button, Empty, Link, Table} from 'acud';
import {ColumnsType} from 'acud/lib/table';
import {FC, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {oracleLinkTypeEnum} from '../../constants';
import styles from './index.module.less';

const PREFIX = 'connection-test';

const StatusTextMap: Record<string, string> = {
  error: '连接失败',
  success: '连接成功',
  processing: '测试中...'
};

enum EStatus {
  Success = 'success',
  Error = 'error',
  Processing = 'processing'
}
interface IComputeResourceItem extends ComputeResourceItem {
  connectionTest?: EStatus;
}

interface IConnectionTestProps {
  /** 校验表单 */
  validateForm: () => Promise<void>;
  /** 回到错误位置 */
  scrollToError: (err) => void;
}

const ConnectionTest: FC<IConnectionTestProps> = ({validateForm, scrollToError}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [loading, setLoading] = useState(false);
  const [connectionList, setConnectionList] = useState<IComputeResourceItem[]>([]);

  // 初始化计算实例列表
  useEffect(() => {
    queryComputeResourceList();
  }, []);

  // 查询计算实例列表
  const queryComputeResourceList = useCallback(async () => {
    setLoading(true);
    try {
      const res = await getComputeResourceList({
        pageNo: 1,
        pageSize: 10000,
        workspaceId,
        engine: Engine.Seatunnel,
        status: TASK_INSTANCE_STATUS.RUNNING
      });
      if (res?.success) {
        setConnectionList(res?.result?.computes || []);
      }
    } catch (err) {}

    setLoading(false);
  }, []);

  // 刷新列表
  const handleRefresh = useCallback(() => {
    queryComputeResourceList();
  }, []);

  // 测试连接
  const handleTest = useCallback(
    async (record: IComputeResourceItem) => {
      // 先校验表单，通过后才能进行连通性测试
      await validateForm()
        .then(async (values: any) => {
          // loading态
          setConnectionList((prev) => {
            return prev.map((item) => {
              if (item.computeId === record.computeId) {
                const updatedItem = {
                  ...item,
                  connectionTest: EStatus.Processing
                };
                return updatedItem;
              }
              return item;
            });
          });

          const {
            type,
            host,
            port,
            user,
            passwd,
            database,
            oracleConnectType,
            version,
            oracleSID,
            oracleServiceName
          } = values;
          testConnection(workspaceId, {
            environment: {
              workspaceId,
              computeId: record.computeId
            },
            datasourceInfo: {
              // HDFS 类型复用新增字段
              ...values,
              type,
              host,
              port,
              username: user,
              password: passwd,
              ...(database ? {database} : {}),
              version,
              connectionType: oracleConnectType,
              connectionValue:
                oracleConnectType === oracleLinkTypeEnum.ORACLE_SID ? oracleSID : oracleServiceName
            }
          }).then((res) => {
            const {success, result} = res;
            setConnectionList((prev) => {
              return prev.map((item) => {
                if (item.computeId === record.computeId) {
                  const updatedItem = {
                    ...item,
                    connectionTest: result?.reachable && success ? EStatus.Success : EStatus.Error
                  };
                  return updatedItem;
                }
                return item;
              });
            });
          });
        })
        .catch((err) => {
          err.errorFields && scrollToError(err.errorFields);
        });
    },
    [validateForm, scrollToError, workspaceId]
  );

  const columns = useMemo(() => {
    const col: ColumnsType<any> = [
      {
        title: '计算实例',
        dataIndex: 'name',
        key: 'name',
        ellipsis: {showTitle: false},
        render: (name) => {
          return <Ellipsis tooltip={name}>{name}</Ellipsis>;
        }
      },
      ...(flags.DatabuilderPrivateSwitch // 私有化版本不展示vpc和subnet字段
        ? []
        : [
            {
              title: 'VPC',
              dataIndex: 'vpcName',
              key: 'vpcName',
              ellipsis: {showTitle: false},
              render: (vpcName) => {
                return (
                  <Ellipsis tooltip={vpcName}>
                    <Link onClick={() => window.open('/network/#/vpc/instance/list', '_blank')}>
                      {vpcName}
                    </Link>
                  </Ellipsis>
                );
              }
            },
            {
              title: '子网',
              dataIndex: 'subnetName',
              key: 'subnetName',
              ellipsis: {showTitle: false},
              render: (subnetName) => {
                return (
                  <Ellipsis tooltip={subnetName}>
                    <Link onClick={() => window.open('/network/#/vpc/subnet/list', '_blank')}>
                      {subnetName}
                    </Link>
                  </Ellipsis>
                );
              }
            }
          ]),
      {
        title: '连通状态',
        width: 200,
        render: (record: IComputeResourceItem) => {
          return (
            <div className={styles['td-test-bar']}>
              <Button
                onClick={() => handleTest(record)}
                disabled={record.connectionTest === EStatus.Processing}
              >
                测试连通性
              </Button>
              {record.connectionTest ? (
                <>
                  <Status status={record.connectionTest} label={StatusTextMap[record.connectionTest]} />
                </>
              ) : (
                <span className={styles['untest']}>
                  <IconSvg type="alert" size={16} />
                  未测试
                </span>
              )}
            </div>
          );
        }
      }
    ];
    return col;
  }, []);

  return (
    <div className={styles[`${PREFIX}`]}>
      <div className={styles[`${PREFIX}-title`]}>
        <span>连通测试</span>
        <Button onClick={handleRefresh}>
          <IconSvg type="refresh" size={12} />
        </Button>
      </div>

      <Table
        className={styles[`${PREFIX}-table`]}
        loading={loading}
        dataSource={connectionList}
        columns={columns}
        rowKey="name"
        pagination={false}
        locale={{
          emptyText: (
            <Empty
              className={styles[`${PREFIX}-table-empty`]}
              image={<IconSvg type="connection-empty" size={80} />}
              description={
                <p className={styles[`${PREFIX}-table-empty-desc`]}>
                  暂无数据，前往{' '}
                  <a
                    onClick={() => {
                      window.open(
                        `${window.location.pathname}#/workspace/compute?tab=dataIntegration&workspaceId=${workspaceId}`,
                        '_blank'
                      );
                    }}
                  >
                    创建计算资源
                  </a>
                </p>
              }
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            />
          )
        }}
      />
    </div>
  );
};

export default ConnectionTest;

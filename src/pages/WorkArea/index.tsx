/**
 * WorkArea 工作区页面入口文件
 * 主要功能:
 *  1.  CatalogTree: 树形目录，显示当前工作区的目录结构
 *  2.  FileList: 文件列表，显示当前目录下的文件和文件夹
 *  3.  Breadcrumb: 面包屑导航，显示当前位置路径
 * 基本逻辑:
 *  1.  初始化时，CatalogTree 和 FileList 都是空的
 *  2.  用户点击 CatalogTree 的某个目录时，FileList 会显示该目录下的文件和文件夹
 *  3.  用户可以在 FileList 中选择文件或文件夹，进行 rename/copy/move 等操作
 *  4.  面包屑组件显示当前位置的路径，用户可以点击面包屑导航到上级目录
 * <AUTHOR>
 */
import React, {useState, useCallback, useEffect, useRef, useContext} from 'react';
import _ from 'lodash';
import CatalogTree from './components/catalog';
import FileList from './components/list';
import BreadCrumb from './components/BreadCrumb';
import {getFolderPath, type GetWorkspaceFolderListResult} from '@api/WorkArea';
import useUrlState from '@ahooksjs/use-url-state';
import {useRequest} from 'ahooks';
import DividerLayout from '@components/LayoutLeftDividerContent';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {CatalogTreeRef} from './components/catalog';
import {FOLDER_NAME_MAP} from './config';
import {useNavigate} from 'react-router-dom';
import {WorkspaceContext} from '@pages/index';
import {Privilege} from '@api/permission/type';
import urls from '@utils/urls';
const cx = classNames.bind(styles);

const WorkArea: React.FC = () => {
  const [urlState, setUrlState] = useUrlState();
  const [defaultUrlState, setDefaultUrlState] = useUrlState(undefined, {navigateMode: 'replace'});
  const [currentDirObj, setCurrentDirObj] = useState<GetWorkspaceFolderListResult>();
  const [path, setPath] = useState<GetWorkspaceFolderListResult[]>([]);
  const catalogTreeRef = useRef<CatalogTreeRef>(null);
  const {workspaceId} = useContext(WorkspaceContext);
  const [privileges, setPrivileges] = useState<Privilege[]>();

  const navigate = useNavigate();
  const {run: getFolderPathRun} = useRequest(getFolderPath, {
    manual: true,
    onSuccess: (res) => {
      if (res.success && res.result) {
        const subMap = _.pick(FOLDER_NAME_MAP, 'TRASH', 'USERS', 'ALL', 'SHARED');
        const nameTrans = res.result.map((item) => ({
          ...item,
          name: subMap[item.type] || item.name
        }));
        setPath(nameTrans);
        const folder = _.find(nameTrans, {id: urlState.folderId});
        if (folder) {
          setCurrentDirObj(folder);
        }
        setPrivileges(res.result[res.result.length - 1].privileges);
      } else {
        // 无权限，跳转到管理页面
        if (res.code === 'AccessDenied') {
          navigate(`${urls.manageWorkspace}`);
          return;
        }
        // 不存在，会重置为home目录
        if (res.code === 'NotExists') {
          // 删除掉url中的folderId，会触发CatalogTree内的重置逻辑
          setUrlState({
            folderId: undefined
          });
        }
      }
    }
  });

  useEffect(() => {
    if (urlState.folderId && workspaceId) {
      getFolderPathRun({
        id: urlState.folderId,
        workspaceId
      });
    }
  }, [urlState.folderId, workspaceId, getFolderPathRun]);

  const handleCurrentDirChange = useCallback(
    (value: GetWorkspaceFolderListResult) => {
      setUrlState({
        folderId: value.id
      });
    },
    [setUrlState]
  );

  const handleDefaultSelect = useCallback(
    (value: GetWorkspaceFolderListResult) => {
      setDefaultUrlState({folderId: value.id});
    },
    [setDefaultUrlState]
  );

  const refreshTreeNode = useCallback((folderId: string) => {
    catalogTreeRef.current?.refreshTreeNode(folderId);
  }, []);

  const handleCreateFolder = useCallback(
    (value: GetWorkspaceFolderListResult) => {
      handleCurrentDirChange(value);
      refreshTreeNode(value.parentId);
    },
    [handleCurrentDirChange, refreshTreeNode]
  );

  return (
    <div className={cx('db-workspace-wrapper', 'work-area-container')}>
      <DividerLayout
        className={cx('h-full w-full')}
        initWidth={300}
        minWidth={200}
        maxWidth={500}
        isSaveLocalStore={false}
      >
        <CatalogTree
          ref={catalogTreeRef}
          currentDirObj={currentDirObj}
          path={path}
          onSelect={handleCurrentDirChange}
          onDefaultSelect={handleDefaultSelect}
        />
        <div className={cx('h-full w-full flex flex-col p-[16px] box-border overflow-auto')}>
          {path.length > 1 && <BreadCrumb path={path} onItemClick={handleCurrentDirChange} />}
          <FileList
            privileges={privileges}
            title={currentDirObj?.name}
            currentDirObj={currentDirObj}
            onFolderClick={handleCurrentDirChange}
            onCreateFolder={handleCreateFolder}
            onFolderChange={refreshTreeNode}
          />
        </div>
      </DividerLayout>
    </div>
  );
};

export default WorkArea;

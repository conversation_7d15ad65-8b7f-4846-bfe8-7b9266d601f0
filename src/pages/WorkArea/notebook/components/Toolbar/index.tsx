import React from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {useNotebookAction} from '../../hook';
import {useHotkeys} from '@hooks/useHotkeys';
import {NotebookHotkeyContext, createToolbarHotkeys} from '../../hotkeys';
import {NotebookPrivilege} from '../../../utils';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import SaveIcon from '@assets/originSvg/notebook/save.svg';
import ExportIcon from '@assets/originSvg/notebook/export.svg';
import DeleteAllCellsIcon from '@assets/originSvg/notebook/delete.svg';
import ClearAllOutputsIcon from '@assets/originSvg/notebook/clearoutputs.svg';
import UndoIcon from '@assets/originSvg/notebook/undo.svg';
import RedoIcon from '@assets/originSvg/notebook/redo.svg';
import ToggleAllLineNumbersIcon from '@assets/originSvg/notebook/line-number.svg';

const cx = classNames.bind(styles);

interface ToolbarProps {
  notebookId: string;
  notebookName: string;
  notebookPrivilege: NotebookPrivilege;
}
export default function Toolbar({notebookId, notebookName, notebookPrivilege}: ToolbarProps) {
  const {canView, canExecute, canModify, canManage} = notebookPrivilege;
  const {save, clearAllOutputs, exportNotebook, toggleAllLineNumbers, deleteAllCells, undo, redo} =
    useNotebookAction(notebookId);

  // 使用快捷键系统
  useHotkeys(
    NotebookHotkeyContext.TOOLBAR,
    createToolbarHotkeys({
      save
    })
  );

  function onExport() {
    exportNotebook(notebookName);
  }

  function onToggleAllLineNumbers() {
    toggleAllLineNumbers();
  }

  function onUndo() {
    undo();
  }

  function onRedo() {
    redo();
  }

  const placement = 'bottom';

  const saveItem = (
    <AuthComponents isAuth={canModify || canManage}>
      <ActionBtn icon="save" onClick={save}>
        保存
      </ActionBtn>
    </AuthComponents>
  );

  const exportItem = (
    <AuthComponents isAuth={canManage}>
      <ActionBtn icon="export" onClick={onExport}>
        导出
      </ActionBtn>
    </AuthComponents>
  );

  const deleteAllCellsItem = (
    <AuthComponents isAuth={canModify || canManage}>
      <ActionBtn icon="delete-all-cells" onClick={deleteAllCells}>
        清空单元格
      </ActionBtn>
    </AuthComponents>
  );

  const undoItem = (
    <AuthComponents isAuth={canModify || canManage}>
      <ActionBtn icon="undo" onClick={onUndo}>
        撤销
      </ActionBtn>
    </AuthComponents>
  );

  const redoItem = (
    <AuthComponents isAuth={canModify || canManage}>
      <ActionBtn icon="redo" onClick={onRedo}>
        重做
      </ActionBtn>
    </AuthComponents>
  );

  const clearAllOutputsItem = (
    <AuthComponents isAuth={canExecute || canModify || canManage}>
      <ActionBtn icon="clear-all-outputs" onClick={clearAllOutputs}>
        清空全部输出
      </ActionBtn>
    </AuthComponents>
  );

  const toggleAllLineNumbersItem = (
    <ActionBtn icon="toggle-all-line-numbers" onClick={onToggleAllLineNumbers}>
      显示行号
    </ActionBtn>
  );

  const splitItem = <div className={cx('divider')}></div>;

  const items = [
    saveItem,
    exportItem,
    deleteAllCellsItem,
    splitItem,
    undoItem,
    redoItem,
    clearAllOutputsItem,
    toggleAllLineNumbersItem
  ];
  return <div className={cx('toolbar')}>{items}</div>;
}

function ActionBtn(props: {icon: string; disabled?: boolean; onClick: (e) => void; children}) {
  const {icon, disabled, onClick, children} = props;
  const clickHandle = (e) => {
    if (disabled) return;
    onClick(e);
  };

  const IconMap = {
    save: SaveIcon,
    export: ExportIcon,
    undo: UndoIcon,
    redo: RedoIcon,
    'delete-all-cells': DeleteAllCellsIcon,
    'clear-all-outputs': ClearAllOutputsIcon,
    'toggle-all-line-numbers': ToggleAllLineNumbersIcon
  };

  const Icon = IconMap[icon];
  return (
    <div className={cx('item-wrap', {disabled})} onClick={clickHandle}>
      <Icon className={cx('item')} />
      {children}
    </div>
  );
}

ActionBtn.displayName = 'ActionBtn';
// 解决外层tooltip组件不生效问题
ActionBtn.__ACUD_BUTTON = true;

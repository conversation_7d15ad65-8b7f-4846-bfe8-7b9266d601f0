import React from 'react';
import type {DataNode} from 'acud/es/tree';
import {Privilege} from '@api/permission/type';

interface treeNode<T> extends DataNode {
  businessData?: T;
}

// 更新树数据的辅助函数
export const updateTreeData = <T>(
  list: treeNode<T>[],
  key: React.Key,
  children: treeNode<T>[]
): treeNode<T>[] => {
  return list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children)
      };
    }
    return node;
  });
};

export type NotebookPrivilege = {
  privilege: string | null;
  canManage: boolean;
  canModify: boolean;
  canExecute: boolean;
  canView: boolean;
};

export function getNotebookPrivilege(notebookPrivilege: string[]): NotebookPrivilege {
  let privilege = null;
  const canWhate = (privilege: string) => ({
    canManage: privilege === Privilege.Manage,
    canModify: privilege === Privilege.Modify,
    canExecute: privilege === Privilege.Execute,
    canView: privilege === Privilege.View
  });
  if (notebookPrivilege.includes(Privilege.Manage)) {
    privilege = Privilege.Manage;
    return {
      privilege,
      ...canWhate(privilege)
    };
  }
  if (notebookPrivilege.includes(Privilege.Modify)) {
    privilege = Privilege.Modify;
    return {
      privilege,
      ...canWhate(privilege)
    };
  }
  if (notebookPrivilege.includes(Privilege.Execute)) {
    privilege = Privilege.Execute;
    return {
      privilege,
      ...canWhate(privilege)
    };
  }
  if (notebookPrivilege.includes(Privilege.View)) {
    privilege = Privilege.View;
    return {
      privilege,
      ...canWhate(privilege)
    };
  }
  return {
    privilege,
    ...canWhate(privilege)
  };
}

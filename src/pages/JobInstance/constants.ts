// 任务实例的状态
export enum JobInstanceStatusEnum {
  /**
   * The instance is initializing.
   */
  INIT = 'INIT',

  /**
   * The instance is actively running.
   */
  RUNNING_EXECUTION = 'RUNNING_EXECUTION',

  /**
   * We have called "pause" on the instance, but it has not yet transitioned to fully paused.
   */
  READY_PAUSE = 'READY_PAUSE',

  /**
   * The instance is fully paused.
   */
  PAUSE = 'PAUSE',

  /**
   * We have called "stop" on the instance, but it has not yet transitioned to fully stopped.
   */
  READY_STOP = 'READY_STOP',

  /**
   * The instance has been completely stopped.
   */
  STOP = 'STOP',

  /**
   * The instance has failed.
   */
  FAILURE = 'FAILURE',
  // 提交失败 一般是 json 配置有问题
  SUBMIT_FAILURE = 'SUBMIT_FAILURE',

  /**
   * The instance completed successfully.
   */
  SUCCESS = 'SUCCESS',
  // 串行等待
  SERIAL_WAIT = 'SERIAL_WAIT'
}

// 任务实例的操作类型
export enum JobInstanceOperateTypeEnum {
  PAUSE = 'pause',
  STOP = 'stop',
  RESUME = 'resume',
  RERUN = 'rerun'
}

/** 重新运行类型 */
export enum JobInstanceRerunTypeEnum {
  // 对应失败点继续
  FROM_FAILURE = 'FROM_FAILURE',
  // 对应从头开始重跑
  FROM_FIRST = 'FROM_FIRST'
}

// 任务实例的 task 状态
export enum JobInstanceTaskStatusEnum {
  /**
   * Task that waiting for be scheduled.
   */
  PENDING = 'PENDING',
  /**
   * The instance is actively running.
   */
  RUNNING_EXECUTION = 'RUNNING_EXECUTION',
  /**
   * The instance is fully paused.
   */
  PAUSE = 'PAUSE',
  /**
   * The instance stop and the running task will be killed.
   */
  KILL = 'KILL',
  /**
   * The instance has failed.
   */
  FAILURE = 'FAILURE',
  /**
   * The instance completed successfully.
   */
  SUCCESS = 'SUCCESS',
  /**
   * All
   */
  ALL = 'ALL'
}

// 任务实例 task 状态
export const JobInstanceTaskStatusMap = {
  [JobInstanceTaskStatusEnum.PENDING]: {
    label: '等待中',
    color: '#5C5F66',
    borderColor: '#D4D6D9',
    tag: 'inactive'
  },
  [JobInstanceTaskStatusEnum.RUNNING_EXECUTION]: {
    label: '运行中',
    color: '#2468F2',
    borderColor: '#A8CAFF',
    tag: 'active'
  },
  [JobInstanceTaskStatusEnum.PAUSE]: {
    label: '暂停',
    color: '#FF9326',
    borderColor: '#FFECD4',
    tag: 'warning'
  },
  [JobInstanceTaskStatusEnum.KILL]: {
    label: '终止',
    color: '#F33E3E',
    borderColor: '#FFDBD9',
    tag: 'error'
  },
  [JobInstanceTaskStatusEnum.FAILURE]: {
    label: '失败',
    color: '#F33E3E',
    borderColor: '#FFDBD9',
    tag: 'inactive',
    icon: 'error'
  },
  [JobInstanceTaskStatusEnum.SUCCESS]: {
    label: '成功',
    color: '#30BF13',
    borderColor: '#D1F2C7',
    tag: 'success-status'
  }
};

// 本地过滤
export const TaskStatusArr = [
  JobInstanceTaskStatusEnum.SUCCESS,
  JobInstanceTaskStatusEnum.FAILURE,
  JobInstanceTaskStatusEnum.RUNNING_EXECUTION,
  JobInstanceTaskStatusEnum.PAUSE,
  JobInstanceTaskStatusEnum.KILL,
  JobInstanceTaskStatusEnum.PENDING
];

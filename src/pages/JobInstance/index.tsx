import {
  Col,
  DatePicker,
  Dropdown,
  Form,
  Link,
  Menu,
  Modal,
  Pagination,
  Row,
  Search,
  Space,
  Table,
  Tag,
  toast,
  Tooltip
} from 'acud';
import locale from 'acud/es/date-picker/locale/zh_CN';
import {useMemoizedFn, useRequest} from 'ahooks';
import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {useNavigate} from 'react-router-dom';
const {RangePicker} = DatePicker;

import {WorkspaceContext} from '@/pages/index';
import {JobRunTriggerTypeChineseEnum, queryJobList, startJob} from '@api/job';
import {
  deleteJobInstance,
  IJobInstance,
  jobInstanceListUser,
  operateJobInstance,
  queryJobInstanceList
} from '@api/jobInstance';
import {Privilege} from '@api/permission/type';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {ExclamationCircle2} from '@baidu/xicon-react-bigdata';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import IconSvg from '@components/IconSvg';
import RefreshButton from '@components/RefreshButton';
import RemoteSelect from '@components/RemoteSelect';
import {authCheck} from '@pages/JobWorkflow/tools';
import {IAppState} from '@store/index';
import {OperateType} from '@utils/enums';
import urls from '@utils/urls';
import {formatSeconds} from '@utils/utils';
import moment from 'moment';
import {useSelector} from 'react-redux';
import JobInstanceRerunModal from './components/RerunModal';
import {JobInstanceOperateTypeEnum, JobInstanceStatusEnum} from './constants';
import styles from './index.module.less';

// 任务实例状态
export const JobInstanceStatusMap = {
  [JobInstanceStatusEnum.INIT]: {
    label: '初始化',
    color: 'inactive'
  },
  [JobInstanceStatusEnum.RUNNING_EXECUTION]: {
    label: '运行中',
    color: 'active'
  },
  [JobInstanceStatusEnum.READY_PAUSE]: {
    label: '准备暂停',
    color: 'inactive'
  },
  [JobInstanceStatusEnum.PAUSE]: {
    label: '暂停',
    color: 'inactive'
  },
  [JobInstanceStatusEnum.READY_STOP]: {
    label: '准备停止',
    color: 'inactive'
  },
  [JobInstanceStatusEnum.FAILURE]: {
    label: '失败',
    color: 'error'
  },
  [JobInstanceStatusEnum.SUBMIT_FAILURE]: {
    label: '提交失败',
    color: 'error'
  },
  [JobInstanceStatusEnum.SUCCESS]: {
    label: '成功',
    color: 'success'
  },
  [JobInstanceStatusEnum.STOP]: {
    label: '停止',
    color: 'inactive'
  },
  [JobInstanceStatusEnum.SERIAL_WAIT]: {
    label: '串行等待',
    color: 'inactive'
  }
};

// 任务实例操作类型 按钮 可点状态
const btnOperateStatusArray: Record<string, string[]> = {
  [OperateType.RERUN]: [
    JobInstanceStatusEnum.FAILURE,
    JobInstanceStatusEnum.SUCCESS,
    JobInstanceStatusEnum.PAUSE,
    JobInstanceStatusEnum.STOP
  ],
  [OperateType.STOP]: [JobInstanceStatusEnum.RUNNING_EXECUTION],
  [OperateType.PAUSE]: [JobInstanceStatusEnum.RUNNING_EXECUTION],
  [OperateType.RESUME]: [JobInstanceStatusEnum.PAUSE, JobInstanceStatusEnum.STOP],
  [OperateType.DELETE]: [
    JobInstanceStatusEnum.SUCCESS,
    JobInstanceStatusEnum.FAILURE,
    JobInstanceStatusEnum.SUBMIT_FAILURE,
    JobInstanceStatusEnum.PAUSE,
    JobInstanceStatusEnum.STOP
  ]
};

// 任务实例操作类型
const OperateTypeMap = {
  [OperateType.PAUSE]: {
    title: '暂停',
    content: '确定要暂停这个任务吗？',
    success: '暂停成功',
    url: JobInstanceOperateTypeEnum.PAUSE
  },
  [OperateType.STOP]: {
    title: '停止',
    content: '确定要停止这个任务吗？',
    success: '停止成功',
    url: JobInstanceOperateTypeEnum.STOP
  },
  [OperateType.RESUME]: {
    title: '恢复',
    content: '确定要恢复这个任务吗？',
    success: '恢复成功',
    url: JobInstanceOperateTypeEnum.RESUME
  },
  [OperateType.RERUN]: {
    title: '重跑',
    content: '确定要重跑这个任务吗？',
    success: '重跑成功',
    url: JobInstanceOperateTypeEnum.RERUN
  }
};
const JobInstance: React.FC<{jobId?: string; jobName?: string}> = ({jobId, jobName}) => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const {workspaceId} = useContext(WorkspaceContext);

  // 刷新时间
  const refreshTime = useSelector((state: IAppState) => state.workflowSlice.refreshTime);
  useEffect(() => {
    runQueryJobList();
  }, [refreshTime]);
  // 表格排序
  const [sorter, setSorter] = useState({});
  // 当前时间 默认查询前2天的数据
  const StartTime = moment().subtract(2, 'day').startOf('day');
  const EndTime = moment().endOf('day');
  const [searchObj, setSearchObj] = useState({
    // 没有 jobId 的时候（运行记录） ，默认查询前2天的数据
    startTimeFrom: !jobId ? moment(StartTime).format('YYYY-MM-DDTHH:mm:ss[Z]') : undefined,
    startTimeTo: !jobId ? moment(EndTime).format('YYYY-MM-DDTHH:mm:ss[Z]') : undefined
  });
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState<{
    pageNo: number;
    pageSize?: number;
  }>({
    pageNo: 1
  });
  const [modalObj, setModalObj] = useState<{
    isVisible: boolean;
    jobInstance?: IJobInstance;
  }>({
    isVisible: false
  });

  const {
    data: dataSource,
    loading,
    run: runQueryJobList
  } = useRequest(
    () =>
      // searchJobIds 需要在searchObj 前面，避免覆盖空
      queryJobInstanceList(
        {...pagination, sorter, filters, searchJobIds: jobId ? [jobId] : undefined, ...searchObj},
        workspaceId
      ),
    {
      refreshDeps: [pagination, sorter, filters, searchObj, workspaceId] //  分页 和 排序变化时刷新
    }
  );

  // 查询的时候 恢复到第一页
  const searchJob = () => {
    setPagination((obj) => ({...obj, pageNo: 1}));
  };

  // 表格操作方法
  const jobInstanceFn = async (jobInstance: IJobInstance, type: OperateType) => {
    switch (type) {
      case OperateType.DETAIL: {
        // 区分工作流 和示例 的详情跳转
        navigate(
          (!jobId ? urls.jobInstanceResult : urls.jobResult) + `?jobInstanceId=${jobInstance.jobInstanceId}`
        );
        break;
      }
      case OperateType.RERUN: {
        setModalObj({
          isVisible: true,
          jobInstance: jobInstance
        });
        break;
      }
      case OperateType.STOP:
      case OperateType.RESUME:
      case OperateType.PAUSE: {
        const obj = OperateTypeMap[type];
        Modal.confirm({
          title: obj.title,
          content: obj.content,
          onOk() {
            return operateJobInstance(jobInstance.workspaceId!, jobInstance.jobInstanceId!, obj.url).then(
              (res) => {
                if (res.success) {
                  toast.success({message: obj.success, duration: 5});
                  searchJob();
                } else {
                  return Promise.reject();
                }
              }
            );
          }
        });
        break;
      }
      case OperateType.DELETE: {
        Modal.confirm({
          title: '删除确定',
          content: `"${jobInstance.jobName}"删除后，工作流中的运行数据将被清空，无法恢复，请谨慎操作`,
          onOk() {
            // 删除工作流 按钮 loading
            return deleteJobInstance(jobInstance.workspaceId!, jobInstance.jobInstanceId!).then((res) => {
              if (res.success) {
                toast.success({message: '删除成功', duration: 5});
                searchJob();
              } else {
                return Promise.reject();
              }
            });
          }
        });
        break;
      }

      default:
        break;
    }
  };
  // 表格列
  const columns: any = useMemo(() => {
    const jobColumns = [
      {
        title: '工作流运行记录ID',
        dataIndex: 'jobInstanceId',
        key: 'jobInstanceId',
        width: 200,
        fixed: 'left',
        ellipsis: {
          showTitle: false
        },
        render: (_, record: IJobInstance) => (
          <a onClick={() => navigate(urls.jobResult + `?jobInstanceId=${record.jobInstanceId}`)}>
            <Ellipsis tooltip={record.jobInstanceId}>{record.jobInstanceId || '-'}</Ellipsis>
          </a>
        )
      }
    ];
    const jobInstanceColumns = [
      {
        title: '运⾏ID',
        dataIndex: 'jobInstanceId',
        key: 'jobInstanceId',
        width: 170,
        fixed: 'left',
        ellipsis: {
          showTitle: false
        },
        render: (_, record: IJobInstance) => (
          <a onClick={() => navigate(urls.jobInstanceResult + `?jobInstanceId=${record.jobInstanceId}`)}>
            <Ellipsis tooltip={record.jobInstanceId}>{record.jobInstanceId || '-'}</Ellipsis>
          </a>
        )
      },
      {
        title: '工作流名称',
        dataIndex: 'jobName',
        key: 'jobName',
        width: 200,
        fixed: 'left',
        ellipsis: {
          showTitle: false
        },
        render: (_, record: IJobInstance) => (
          <a key={OperateType.DETAIL} onClick={() => navigate(urls.jobDetail + `?jobId=${record.jobId}`)}>
            <Ellipsis tooltip={record.jobName}>{record.jobName || '-'}</Ellipsis>
          </a>
        )
      },
      {
        title: '工作流ID',
        dataIndex: 'jobName',
        key: 'jobName',
        width: 120,
        ellipsis: {
          showTitle: false
        },
        render: (_, record: IJobInstance) => <Ellipsis tooltip={record.jobId}>{record.jobId || '-'}</Ellipsis>
      }
    ];

    const columnsList = [
      {
        title: '状态',
        dataIndex: 'status',
        key: 'searchStatuses',
        width: 96,
        filters: Object.keys(JobInstanceStatusMap).map((key) => ({
          text: JobInstanceStatusMap[key].label,
          value: key
        })),
        render: (status, record) => (
          <Space size={0}>
            <Tooltip title={status === JobInstanceStatusEnum.SUBMIT_FAILURE ? record.errorMsg : ''}>
              <Tag color={JobInstanceStatusMap[status]?.color}>
                <div className="flex items-center gap-1">
                  {JobInstanceStatusMap[status]?.label}
                  {status === JobInstanceStatusEnum.SUBMIT_FAILURE && (
                    <ExclamationCircle2 theme="line" size={16} strokeLinejoin="round" />
                  )}
                </div>
              </Tag>
            </Tooltip>
          </Space>
        )
      },
      {
        title: '运行类型',
        dataIndex: 'triggerType',
        key: 'triggerTypes',
        width: 110,
        filterMultiple: false,
        filters: Object.keys(JobRunTriggerTypeChineseEnum).map((key) => ({
          text: JobRunTriggerTypeChineseEnum[key],
          value: key
        })),
        render: (triggerType) => JobRunTriggerTypeChineseEnum[triggerType]
      },
      {
        title: '调度时间',
        dataIndex: 'scheduleTime',
        key: 'scheduleTime',
        width: 154
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        width: 154,
        sorter: true
      },
      {
        title: '结束时间',
        dataIndex: 'endTime',
        key: 'endTime',
        width: 154,
        sorter: true
      },
      {
        title: '运行时长',
        dataIndex: 'durationSec',
        key: 'durationSec',
        width: 80,
        render: (durationSec) => (
          <Ellipsis tooltip={formatSeconds(durationSec)}>{formatSeconds(durationSec)}</Ellipsis>
        )
      },

      {
        title: '运行用户',
        dataIndex: 'runUserName',
        key: 'runUserName',
        width: 120,
        render: (runUserName) => <Ellipsis tooltip={runUserName}>{runUserName || '-'}</Ellipsis>
      },
      {
        title: '操作',
        key: 'operate',
        fixed: 'right',
        width: 120,
        render: (_, record: IJobInstance) => {
          return (
            <Space>
              <Link key={OperateType.DETAIL} onClick={() => jobInstanceFn(record, OperateType.DETAIL)}>
                查看
              </Link>
              <AuthComponents isAuth={authCheck(record.privileges, Privilege.Execute)}>
                <Link
                  key={OperateType.RERUN}
                  disabled={!btnOperateStatusArray[OperateType.RERUN]?.includes(record.status)}
                  onClick={() => jobInstanceFn(record, OperateType.RERUN)}
                >
                  <span>重跑</span>
                </Link>
              </AuthComponents>
              <Dropdown
                overlayStyle={{width: 100}}
                label={<IconSvg type="more" size={16} />}
                overlay={
                  <Menu>
                    <AuthMenuItem
                      isAuth={authCheck(record.privileges, Privilege.Execute)}
                      disabled={!btnOperateStatusArray[OperateType.STOP]?.includes(record.status)}
                      key={OperateType.STOP}
                      onClick={() => jobInstanceFn(record, OperateType.STOP)}
                    >
                      <span>停止</span>
                    </AuthMenuItem>
                    <AuthMenuItem
                      isAuth={authCheck(record.privileges, Privilege.Execute)}
                      disabled={!btnOperateStatusArray[OperateType.PAUSE]?.includes(record.status)}
                      key={OperateType.PAUSE}
                      onClick={() => jobInstanceFn(record, OperateType.PAUSE)}
                    >
                      <span>暂停</span>
                    </AuthMenuItem>
                    <AuthMenuItem
                      isAuth={authCheck(record.privileges, Privilege.Execute)}
                      disabled={!btnOperateStatusArray[OperateType.RESUME]?.includes(record.status)}
                      key={OperateType.RESUME}
                      onClick={() => jobInstanceFn(record, OperateType.RESUME)}
                    >
                      <span>恢复</span>
                    </AuthMenuItem>
                    <AuthMenuItem
                      isAuth={authCheck(record.privileges, Privilege.Execute)}
                      disabled={!btnOperateStatusArray[OperateType.DELETE]?.includes(record.status)}
                      key={OperateType.DELETE}
                      onClick={() => jobInstanceFn(record, OperateType.DELETE)}
                    >
                      <span>删除</span>
                    </AuthMenuItem>
                  </Menu>
                }
              />
            </Space>
          );
        }
      }
    ];
    return !jobId ? [...jobInstanceColumns, ...columnsList] : [...jobColumns, ...columnsList];
  }, [jobId]);

  const showTotal = useCallback(() => {
    return dataSource?.result?.totalCount ? `共 ${dataSource?.result?.totalCount} 条` : '';
  }, [dataSource?.result?.totalCount]);

  // 处理表格变化（分页、排序、筛选）_pagination没有使用
  const handleTableChange = (_pagination, filters, sorter) => {
    setSorter(sorter);
    setFilters(filters);
  };

  // 重跑弹窗提交 flag 控制是否刷新页面
  const handleModalSubmit = (flag: boolean) => {
    setModalObj({
      isVisible: false,
      jobInstance: undefined
    });

    if (flag) {
      searchJob();
    }
  };

  // 切换查询条件时触发搜索
  const searchFn: any = (searchObj?: {jobInstanceId?: string}) => {
    console.log(form.getFieldsValue());
    const obj = {...form.getFieldsValue(), ...searchObj};
    if (obj.startTimeRange) {
      obj.startTimeFrom = obj.startTimeRange[0].format('YYYY-MM-DDTHH:mm:ss[Z]');
      obj.startTimeTo = obj.startTimeRange[1].format('YYYY-MM-DDTHH:mm:ss[Z]');
      delete obj.startTimeRange;
    }
    if (obj.endTimeRange) {
      obj.endTimeFrom = obj.endTimeRange[0].format('YYYY-MM-DDTHH:mm:ss[Z]');
      obj.endTimeTo = obj.endTimeRange[1].format('YYYY-MM-DDTHH:mm:ss[Z]');
      delete obj.endTimeRange;
    }
    setSearchObj(obj);
    // 查询的时候 恢复到第一页
    setPagination((pagination) => ({...pagination, pageNo: 1}));
  };
  // 运行任务
  const runTask = useMemoizedFn(() => {
    Modal.confirm({
      title: '运行确定',
      content: `确定要运行"${jobName}"？`,
      onOk() {
        return startJob(workspaceId, jobId).then((res) => {
          if (res.success) {
            toast.success({
              message: '运行提交成功',
              duration: 5
            });
            runQueryJobList();
          } else {
            return Promise.reject();
          }
        });
      }
    });
  });

  return (
    <div className={(!jobId ? 'db-workspace-wrapper ' : '') + styles['job-instance']}>
      {/* 没有 jobId 的时候 显示搜索框 */}
      {jobId ? (
        <>
          {/* <div className="flex justify-end gap-2 mb-2">
            <Button
              type="primary"
              onClick={() => {
                runTask();
              }}
            >
              运行
            </Button>
            <RefreshButton onClick={runQueryJobList}></RefreshButton>
          </div> */}
        </>
      ) : (
        <div>
          <div className={styles['operation-title']}>运行记录</div>
          <div className={styles['operation-container']}>
            <Form
              form={form}
              name="baseForm"
              className={styles['form-container']}
              labelWidth={60}
              initialValues={{
                startTimeRange: [moment(StartTime), moment(EndTime)]
              }}
            >
              <Row gutter={8}>
                <Col flex="100 0 200px">
                  <Form.Item name="jobInstanceId" className="flex-nowrap">
                    <Search
                      placeholder="请输入运行ID"
                      allowClear
                      onSearch={(v) => {
                        searchFn({jobInstanceId: v});
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col flex="100 0 150px">
                  <Form.Item name="searchJobIds">
                    <RemoteSelect
                      dropdownMatchSelectWidth={false}
                      showTitle={true}
                      dropdownStyle={{maxWidth: 300}}
                      maxTagCount={'responsive'}
                      allowClear
                      mode="multiple"
                      optionFilterProp="label"
                      objName="name"
                      objId="jobId"
                      queryList={queryJobList}
                      params={[{pageSize: 10000, pageNo: 1}, workspaceId]}
                      onChange={(v) => {
                        searchFn({searchJobIds: v});
                      }}
                      placeholder="全部工作流"
                    />
                  </Form.Item>
                </Col>

                <Col flex="100 0 150px">
                  <Form.Item name="searchUserIds">
                    <RemoteSelect
                      allowClear
                      dropdownMatchSelectWidth={false}
                      dropdownStyle={{maxWidth: 300}}
                      dropdownClassName={styles['select-dropdown']}
                      maxTagCount={'responsive'}
                      mode="multiple"
                      objName="runUsername"
                      optionFilterProp="label"
                      objId="runUserId"
                      queryList={jobInstanceListUser}
                      params={[workspaceId]}
                      onChange={(v) => {
                        searchFn({searchUserIds: v});
                      }}
                      placeholder="全部用户"
                    />
                  </Form.Item>
                </Col>
                <Col flex="100 0 340px">
                  <Form.Item name="startTimeRange">
                    <RangePicker
                      className="w-full"
                      locale={locale}
                      placeholder={['开始时间：开始', '结束']}
                      showTime
                      onChange={(v) => {
                        searchFn({startTimeRange: v});
                      }}
                    />
                  </Form.Item>
                </Col>

                <Col flex="100 0 340px">
                  <Form.Item name="endTimeRange">
                    <RangePicker
                      placeholder={['结束时间：开始', '结束']}
                      className="w-full"
                      locale={locale}
                      showTime
                      onChange={(v) => {
                        searchFn({endTimeRange: v});
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col flex="1 0 50px" className="flex justify-end pb-4">
                  <RefreshButton onClick={runQueryJobList}></RefreshButton>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      )}

      <Table
        scroll={{x: 1228}}
        dataSource={dataSource?.result?.result}
        columns={columns}
        rowKey="jobInstanceId"
        loading={{
          loading,
          size: 'small'
        }}
        pagination={false}
        onChange={handleTableChange}
      />

      <div className={styles['pagination-container']}>
        <Pagination
          showSizeChanger
          showQuickJumper
          current={pagination.pageNo}
          showTotal={showTotal}
          total={dataSource?.result?.totalCount || 0}
          onChange={(page, pageSize) => {
            setPagination({
              pageNo: page,
              pageSize: pageSize
            });
          }}
        />
      </div>

      <JobInstanceRerunModal
        isVisible={modalObj.isVisible}
        jobInstance={modalObj.jobInstance}
        onSubmit={handleModalSubmit}
      />
    </div>
  );
};

export default JobInstance;

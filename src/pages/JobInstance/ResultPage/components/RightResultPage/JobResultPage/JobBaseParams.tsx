import {JobRunTriggerTypeChineseEnum} from '@api/job';
import {IJobInstance} from '@api/jobInstance';
import IconSvg from '@components/IconSvg';
import {JobInstanceStatusMap} from '@pages/JobInstance';
import {
  JobExecutionTypeChineseMap,
  JobExecutionTypeEnum,
  JobFailureStrategyChineseMap,
  TaskAlertSceneChineseMap
} from '@pages/JobWorkflow/constants';
import {formatSeconds} from '@utils/utils';
import {Table} from 'acud';
import React, {useMemo} from 'react';
import DescriptionList from '../../DescriptionList';

const JobBaseParams: React.FC<{
  jobInstance: IJobInstance;
}> = ({jobInstance}) => {
  const infoList = [
    {
      label: '工作流名称',
      value: jobInstance?.jobName
    },
    {
      label: '工作流ID',
      value: jobInstance?.jobId
    },
    {
      label: '运行记录ID',
      value: jobInstance?.jobInstanceId
    },
    {
      label: '运行状态',
      value: JobInstanceStatusMap[jobInstance?.jobStatus]?.label
    },
    {
      label: '调度时间',
      value: jobInstance?.scheduleTime
    },
    {
      label: '开始时间',
      value: jobInstance?.startTime
    },
    {
      label: '结束时间',
      value: jobInstance?.endTime
    },
    {
      label: '运行时长',
      value: formatSeconds(jobInstance?.durationSec)
    },
    {
      label: '运行类型',
      value: JobRunTriggerTypeChineseEnum[jobInstance?.triggerType]
    },
    {
      label: '运行用户',
      value: jobInstance?.runUsername
    },
    {
      label: '描述',
      value: jobInstance?.description
    }
  ];

  const runConfigList = useMemo(() => {
    return [
      {
        label: '执行策略',
        value: JobExecutionTypeChineseMap[jobInstance?.executionType]
      },
      {
        label: '最大并行数',
        value: jobInstance?.maxConcurrency,
        isHidden: jobInstance?.executionType !== JobExecutionTypeEnum.SERIAL_WAIT
      },
      {
        label: '失败策略',
        value: JobFailureStrategyChineseMap[jobInstance?.failureStrategy]
      }
    ];
  }, [jobInstance]);

  const column = [
    {
      title: '用户',
      dataIndex: 'alertUser',
      width: 200
    },
    {
      title: '方式',
      dataIndex: 'alertType',
      width: 200
    },
    {
      title: '告警策略',
      dataIndex: 'alertScenes',
      width: 200,
      render: (text: string[], record: any) => {
        return text?.map((item: string) => TaskAlertSceneChineseMap[item]).join('、');
      }
    }
  ];

  return (
    <>
      <DescriptionList infoList={infoList} colon={false} />

      <div className={'form-title'}>
        <IconSvg size={16} type="workflow-detail" />
        <span className={'form-title-text'}>运行配置</span>
      </div>
      <DescriptionList infoList={runConfigList} colon={false} />

      {/* <div className={'form-title'}>
        <IconSvg size={16} type="workflow-detail" />
        <span className={'form-title-text'}>通知告警</span>
      </div>
      <Table columns={column} dataSource={jobInstance?.alertStrategyList} pagination={false} /> */}
    </>
  );
};

export default JobBaseParams;

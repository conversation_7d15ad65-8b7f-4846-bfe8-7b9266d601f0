import React, {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import Monaco<PERSON><PERSON><PERSON> from 'react-monaco-editor';
import SqlEdit from '../SqlEdit';
import {Button, toast} from 'acud';

import styles from './index.module.less';
import {validateDDL} from '@api/metaRequest';
import {WorkspaceContext} from '@pages/index';

interface ColumnData {
  name: string;
  comment: string;
  type: string;
  isPrimaryKey: boolean;
  precision: number;
  scale: number;
}

interface DDLCreateMonacoProps {
  value?: string;
  catalog: string;
  schema: string;
  table: string;
  dataSource: string;
  columns: ColumnData[];
  isReadOnly: boolean;
  comment?: string;
  onChange?: (statement: string) => void;
}

export const getTypeByPrecision = (type, precision: number, scale?: number) => {
  if (type === 'DECIMAL') {
    return `DECIMAL(${precision}, ${scale})`;
  }
  if (['VARCHAR', 'CHAR', 'DATETIME'].includes(type)) {
    return `${type.toUpperCase()}(${precision})`;
  }
  return type.toUpperCase();
};

export const DDLStatementMonaco: React.FC<DDLCreateMonacoProps> = ({
  value,
  catalog,
  schema,
  table,
  dataSource,
  columns,
  isReadOnly,
  comment = '',
  onChange
}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  const [sql, setSql] = useState<string>();

  const defaultStatement = useMemo(() => {
    const primaryKeys = columns.filter((col) => col.isPrimaryKey);
    const primaryKeysStatement = columns.filter((col) => col.isPrimaryKey).map((col) => `\`${col.name}\``);
    const uniqueKeysStatement = primaryKeys.length
      ? `UNIQUE KEY (${primaryKeysStatement.join(', ')})`
      : `DUPLICATE KEY(\`${columns[0]?.name}\`)`;
    return `${uniqueKeysStatement}\nCOMMENT '${comment}'\nDISTRIBUTED BY HASH(\`${primaryKeys[0]?.name || columns[0]?.name}\`) BUCKETS 10;\n`;
  }, [columns, comment]);

  // 固定的建表statement
  const fieldStatement = useMemo(() => {
    const columnStatement = columns.map(
      (column) =>
        `   \`${column.name}\` ${getTypeByPrecision(column.type, column.precision, column.scale)}${column.isPrimaryKey ? ' NOT NULL' : ''} COMMENT "${column.comment}"`
    );
    return `CREATE TABLE ${table} (\n${columnStatement.join(',\n')}\n)\n`;
  }, [columns, table]);

  useEffect(() => {
    // replace 替换，通过正则 去除 CREATE TABLE 到 \n)\n 之间的内容
    const sql = isReadOnly || value ? value.replace(/(^CREATE TABLE[\s\S]*?\n\)\n)/, '') : defaultStatement;
    setSql(sql);
  }, [defaultStatement, fieldStatement, isReadOnly, value]);

  const statement = useMemo(() => {
    if (!columns?.length) {
      return '';
    }
    return `${fieldStatement}${sql}`;
  }, [columns?.length, fieldStatement, sql]);

  // 格式校验回调
  const validateStatement = useCallback(async () => {
    const params = {
      catalogName: catalog,
      schemaName: schema,
      dataSourceFormat: dataSource.toUpperCase(),
      ddl: statement
    };
    try {
      const res = await validateDDL(workspaceId, params);
      if (res.result.success) {
        toast.success({
          message: '校验成功！',
          duration: 5
        });
      } else {
        toast.error({
          message: res.result.msg,
          duration: 5
        });
      }
    } catch (err) {
      console.error('校验接口错误', err);
    }
  }, [catalog, dataSource, schema, statement, workspaceId]);

  useEffect(() => {
    onChange && onChange(statement);
  }, [onChange, statement]);

  const onSqlChange = useCallback((value) => {
    setSql(value);
  }, []);

  // 恢复推荐
  const reset = useCallback(() => {
    setSql(defaultStatement);
  }, [defaultStatement]);

  return (
    <div className={styles['container']}>
      <div className={styles['header']}>
        自动建表语句
        {isReadOnly ? null : (
          <div>
            <Button onClick={reset} className="mr-[8px]">
              恢复推荐
            </Button>
            <Button type="primary" onClick={validateStatement}>
              格式校验
            </Button>
          </div>
        )}
      </div>
      <SqlEdit value={fieldStatement} isReadOnly height={150}></SqlEdit>
      <SqlEdit value={sql} onChange={onSqlChange} isReadOnly={isReadOnly}></SqlEdit>
    </div>
  );
};

import React, {use<PERSON><PERSON>back, useContext, useEffect, useState} from 'react';
import moment from 'moment';

import {Button, Form, Input, InputNumber, Link, Select, Space, toast, Tooltip, DatePicker} from 'acud';
import {
  JobType,
  FileSinkType as SinkType,
  FileSourceType as SourceType,
  JobDetailBase,
  JobCreateReq,
  JobUpdateReq,
  DuplicateFileStrategyType
} from '@api/integration/type';
import {DataSourceMap, SourceOptions} from '../constants';
import {RULE} from '@utils/regs';
import * as http from '@api/integration';
import {WorkspaceContext} from '@pages/index';
import IconSvg from '@components/IconSvg';
import useUrlState from '@ahooksjs/use-url-state';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import FilePathSelect, {PathLevel} from '@components/FilePathSelect';
import classNames from 'classnames/bind';
import {onStart} from '../FileColletRunModal';
import OnOpenModal, {ModalTypeEnum} from '../../SqlCollect/OfflineCollect/components/OnOpenModal';
import TimeFilterInput from '../components/TimeFilterInput';
import {getTimeFilterValue, createTimeFilterValidator} from '../utils';

import styles from './index.module.less';
import {Engine, getComputeResourceList} from '@api/Compute';
import {queryConnectionList} from '@api/connection';
import {EnableSourceFileFilterModOptions, SourceFileFilterModStatus, TimeFilterType} from '../constants';
import {Privilege} from '@api/permission/type';
const {Option} = Select;

const cx = classNames.bind(styles);

const DestOptions = [
  {
    label: 'DataBuilder Catalog',
    value: SinkType.Catalog
  }
];

const DuplicateFileStrategyOptions = [
  {
    label: '覆盖',
    value: DuplicateFileStrategyType.Overwrite,
    describe: '替换目标路径中的同名文件'
  },
  {
    label: '跳过',
    value: DuplicateFileStrategyType.Skip,
    describe: '跳过写入该文件'
  },
  {
    label: '重命名',
    value: DuplicateFileStrategyType.Rename,
    describe: '在文件名前增加${transactionId}_避免冲突'
  }
];

export enum Mode {
  Edit = 'edit',
  Create = 'create'
}

/**
 * 文件采集-创建
 */
const FileCollectCreate: React.FC = () => {
  const [form] = Form.useForm();
  const [sourceForm] = Form.useForm();
  const [destForm] = Form.useForm();
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState] = useUrlState({jobId: '', sourceType: '', sourceConnectionId: '', isPublished: false});
  const [mode, setMode] = useState<Mode>(Mode.Create);
  // 数据源
  const [sourceConnectionId, setSourceConnectionId] = useState<{
    loading: boolean;
    options: {value: string; label: string}[];
  }>({loading: false, options: []});
  // 计算实例
  const [computedList, setComputedList] = useState<{
    loading: boolean;
    options: {value: string; label: string}[];
  }>({loading: false, options: []});
  // 保存按钮loading状态
  const [saving, setSaving] = useState(false);
  // 用于覆盖策略下方 extra 文案联动
  const duplicateFileStrategy = Form.useWatch('duplicateFileStrategy', destForm);

  // 获取详情
  const getDetails = useCallback(async () => {
    const res = await http.getJobDetails(workspaceId, urlState.jobId, {
      isPublished: urlState.isPublished
    });
    if (!res.result) {
      return;
    }
    const {name, compute, description, sourceConfig, sinkConfig} = res.result;
    const {
      sourceType,
      sourceConnectionId,
      sourceFilePath,
      sourceFileFilterPattern,
      sourceFileNameExtension,
      sourceFileFilterModMin,
      sourceFileFilterModMax,
      parallelism
    } = sourceConfig;
    const {sinkType, sinkVolume, duplicateFileStrategy} = sinkConfig;

    // 如果是 Edit 态则取获取到的 sourceType 来加载连接列表，避免对 URL 参数的过度依赖
    if (sourceType) {
      loadConnectionList(sourceType);
    }

    // 信息回填
    form.setFieldsValue({
      name,
      compute: compute.computeId,
      description,
      parallelism
    });

    // Edit 数据回填时，智能判断时间过滤类型的辅助函数
    const determineTimeFilterType = (value: string, otherValue?: string): TimeFilterType => {
      // 如果当前值为空且另一个值也为空，则为固定时间（默认状态）（但一般不会出现这种情况，因为会被直接判定为不过滤）
      if (!value && !otherValue) return TimeFilterType.Fixed;

      // 如果当前值为空但另一个值不为空，则判断为不限
      if (!value && otherValue) return TimeFilterType.Unlimited;

      // 如果包含动态时间表达式，判断为动态时间
      if (value.includes('${logicTime')) {
        return TimeFilterType.Dynamic;
      }

      // 尝试解析为moment时间，如果成功则为固定时间
      if (
        moment(value, 'YYYY-MM-DD HH:mm:ss', true).isValid() ||
        moment(value, 'YYYY-MM-DD', true).isValid() ||
        moment(value).isValid()
      ) {
        return TimeFilterType.Fixed;
      }

      // 无法解析时，回退到动态时间（保持原值，让用户手动调整）
      console.warn(`无法解析时间值 "${value}"，回退到动态时间模式`);
      return TimeFilterType.Dynamic;
    };

    // 检查是否启用了时间过滤
    const hasTimeFilter = !!(sourceFileFilterModMin || sourceFileFilterModMax);

    sourceForm.setFieldsValue({
      sourceType,
      sourceConnectionId,
      sourceFilePath,
      sourceFileFilterPattern,
      sourceFileNameExtension,
      // 设置时间过滤策略 - 如果有时间过滤值则启用
      enableSourceFileFilterMod: hasTimeFilter
        ? SourceFileFilterModStatus.Enabled
        : SourceFileFilterModStatus.Disabled,
      // 回填时间过滤相关字段，转换为新的数据结构
      sourceFileFilterModMin: hasTimeFilter
        ? {
            type: determineTimeFilterType(
              sourceConfig.sourceFileFilterModMin,
              sourceConfig.sourceFileFilterModMax
            ),
            value: sourceConfig.sourceFileFilterModMin
          }
        : undefined,
      sourceFileFilterModMax: hasTimeFilter
        ? {
            type: determineTimeFilterType(
              sourceConfig.sourceFileFilterModMax,
              sourceConfig.sourceFileFilterModMin
            ),
            value: sourceConfig.sourceFileFilterModMax
          }
        : undefined
    });

    // 旧数据兼容，旧数据 duplicateFileStrategy 为 undefined 或为空字符串时，默认值为 Overwrite
    if (typeof duplicateFileStrategy === 'string' && duplicateFileStrategy.length) {
      destForm.setFieldsValue({sinkType, sinkVolume, duplicateFileStrategy});
    } else {
      destForm.setFieldsValue({
        sinkType,
        sinkVolume,
        duplicateFileStrategy: DuplicateFileStrategyType.Overwrite
      });
    }
  }, [destForm, form, sourceForm, urlState.jobId, workspaceId]);

  useEffect(() => {
    const mode = urlState.jobId ? Mode.Edit : Mode.Create;
    setMode(mode);
    // 编辑-回填任务信息
    if (mode === Mode.Edit) {
      getDetails();
    } else {
      // 新建-回填 url 中数据源类型
      sourceForm.setFieldValue('sourceType', urlState.sourceType || SourceType.FTP);
      urlState?.sourceConnectionId &&
        sourceForm.setFieldValue('sourceConnectionId', urlState.sourceConnectionId);
    }
  }, [getDetails, sourceForm, urlState.jobId, urlState.sourceConnectionId, urlState.sourceType]);

  const handleCreateDatasource = () => {
    window.open(
      `${window.location.pathname}#${urls.connection}?workspaceId=${workspaceId}&mode=create`,
      '_blank'
    );
  };

  const nameRules = [
    {required: true, message: '请输入任务名称'},
    {
      validator: (_, value) => {
        // 校验特殊字符和长度限制
        if (!RULE.workflowName.test(value)) {
          return Promise.reject(new Error(RULE.workflowNameText));
        }
        return Promise.resolve();
      }
    }
  ];

  // 创建/编辑任务
  const saveTask = useCallback(async () => {
    try {
      await form.validateFields();
      await sourceForm.validateFields();
      await destForm.validateFields();
    } catch (err) {
      console.log(err);
      return;
    }
    const fields = form.getFieldsValue();
    const sourceFields = sourceForm.getFieldsValue();
    const destFields = destForm.getFieldsValue();

    const {name, compute, description, parallelism} = fields;

    try {
      // 异步校验任务名称是否重复
      const res = await http.getIntegrationJobList(workspaceId, {namePattern: name, type: JobType.File});
      if (!res.success) {
        return;
      } else if (res.result?.jobs.find((item) => item.jobId !== urlState?.jobId && item.name === name)) {
        toast.error({message: '该任务名称已存在，请重新输入', duration: 5});
        return;
      }
    } catch {
      return;
    }

    const {
      sourceType,
      sourceConnectionId,
      sourceFilePath,
      sourceFileFilterPattern,
      sourceFileNameExtension,
      sourceFileFilterModMin,
      sourceFileFilterModMax
    } = sourceFields;

    const {sinkType, sinkVolume, duplicateFileStrategy} = destFields;

    // 构建任务创建/更新请求的基础参数
    const baseParams: JobDetailBase = {
      name,
      compute: {computeId: compute, name: computedList.options.find((item) => item.value === compute)?.label},
      type: JobType.File,
      description,
      sourceConfig: {
        sourceType,
        sourceConnectionId,
        sourceFilePath,
        sourceFileFilterPattern,
        sourceFileNameExtension,
        // 只有启用时间过滤时才传递时间相关字段
        sourceFileFilterModMin: getTimeFilterValue(
          sourceFileFilterModMin,
          sourceFields.enableSourceFileFilterMod
        ),
        sourceFileFilterModMax: getTimeFilterValue(
          sourceFileFilterModMax,
          sourceFields.enableSourceFileFilterMod
        ),
        parallelism
      },
      sinkConfig: {
        sinkType,
        sinkVolume,
        duplicateFileStrategy
      }
    };

    try {
      if (mode === Mode.Create) {
        // 任务创建参数，当前版本与基础参数一致
        const createParams: JobCreateReq = {
          ...baseParams
        };
        const res = await http.createIntegrationJob(workspaceId, createParams);
        return res?.result?.jobId;
      }

      // 任务更新参数，当前版本比基础参数多一个 isPublished 字段
      const updateParams: JobUpdateReq = {
        ...baseParams,
        isPublished: urlState.isPublished
      };
      await http.updateIntegrationJob(workspaceId, urlState.jobId, updateParams);
      return urlState.jobId;
    } catch (err) {
      console.error('更新失败', err);
    }
  }, [computedList?.options, destForm, form, mode, sourceForm, urlState.jobId, workspaceId]);

  // 保存（运行）
  const submit = useCallback(
    (isRun = false) =>
      async () => {
        try {
          setSaving(true);
          // 创建从请求的返回中获取
          const jobId = await saveTask();
          if (jobId) {
            if (isRun) {
              const jobItem = {
                jobId,
                name: form.getFieldValue('name')
              };
              OnOpenModal(
                ModalTypeEnum.FilePreCheck,
                [jobItem],
                workspaceId,
                () => {
                  // 运行成功后回到列表页
                  navigate(urls.integration);
                },
                navigate
              );
            } else {
              // 仅保存，直接回到列表页
              navigate(urls.integration);
            }
          }
        } catch (err) {
          console.error('保存失败', err);
        } finally {
          setSaving(false);
        }
      },
    [saveTask, workspaceId, navigate]
  );

  // 加载计算资源列表
  const loadConnectionList = useCallback(
    async (type) => {
      setSourceConnectionId((data) => ({...data, loading: true}));
      try {
        const res = await queryConnectionList(
          workspaceId,
          {pageNo: 1, pageSize: 100, type},
          Privilege.UseConnection
        );
        const data = res.success ? res.result.connections : [];
        const options = data.map((item) => ({label: item.name, value: item.name}));
        setSourceConnectionId({options, loading: false});
      } catch (err) {
        console.error(err);
        setSourceConnectionId((data) => ({...data, loading: false}));
      }
    },
    [workspaceId]
  );

  // 加载计算资源列表
  const loadComputeList = useCallback(async () => {
    setComputedList((data) => ({...data, loading: true}));
    try {
      const res = await getComputeResourceList({
        workspaceId,
        engine: Engine.Seatunnel
      });
      const data = res.success ? res.result.computes.filter((item) => item.status === 'RUNNING') : [];
      const options = data.map((item) => ({label: item.name, value: item.computeId}));
      setComputedList({options, loading: false});
    } catch (err) {
      console.error(err);
      setComputedList((data) => ({...data, loading: false}));
    }
  }, [workspaceId]);

  useEffect(() => {
    loadComputeList();
    // 如果是新建态，url 中有 sourceType，需要依据 URL 中的 sourceType 加载连接列表
    if (urlState.sourceType) {
      loadConnectionList(urlState.sourceType);
    }
  }, [loadComputeList, loadConnectionList, urlState.sourceType]);

  // 取消
  const onCancel = () => navigate(urls.integration);

  const onSourceTypeChange = (value) => {
    sourceForm.setFieldValue('sourceConnectionId', '');
    loadConnectionList(value);
  };

  const backToList = () => {
    navigate(urls.integration);
  };

  return (
    <div className={cx('container')}>
      <div className={cx('content')}>
        <Space className={cx('content-title')} onClick={backToList}>
          <IconSvg type="left" size={16} color="#151B26" />
          {mode === Mode.Create ? '创建文件离线采集' : `编辑 ${form.getFieldValue('name')}`}
        </Space>
        <Form form={form} labelAlign="left" labelWidth="96px" colon={false} initialValues={{parallelism: 1}}>
          <div className={cx('group-title')}>基本配置</div>

          <Form.Item label="任务名称" name="name" rules={nameRules}>
            <Input placeholder="请输入任务名称" width={428} />
          </Form.Item>

          <Form.Item label="任务描述" name="description">
            <Input.TextArea limitLength={500} placeholder="请输入任务描述" width={428} />
          </Form.Item>

          <div className={cx('group-title')}>源端与目标端配置</div>
          <div className={cx('data-config')}>
            <div className={cx('form-box')}>
              <div className={cx('form-box-title')}>源端配置</div>
              <Form
                form={sourceForm}
                labelAlign="left"
                labelWidth="96px"
                colon={false}
                className={cx('form-box-content')}
                initialValues={{
                  enableSourceFileFilterMod: SourceFileFilterModStatus.Disabled
                }}
              >
                <Form.Item
                  label="源端类型"
                  name="sourceType"
                  rules={[{required: true, message: '请选择源端类型'}]}
                >
                  <Select placeholder="请选择源端类型" onChange={onSourceTypeChange} style={{width: 434.8}}>
                    {SourceOptions.map((item) => (
                      <Option key={item.value} value={item.value}>
                        <div className={cx('source-option')}>
                          <IconSvg
                            type={DataSourceMap[item.value].icon}
                            color={DataSourceMap[item.value].color}
                            className="bordered-circle-icon mr-[8px]"
                            size={14}
                          />
                          {item.label}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item label="数据源名称" required>
                  <Form.Item
                    noStyle
                    name="sourceConnectionId"
                    rules={[{required: true, message: '请选择数据源'}]}
                  >
                    <Select
                      showSearch
                      placeholder="请选择数据源"
                      loading={sourceConnectionId.loading}
                      options={sourceConnectionId.options}
                      style={{width: 'calc(100% - 48px)'}}
                      dropdownRender={(menu) => (
                        <div>
                          {menu}
                          <Button type="actiontext" onClick={handleCreateDatasource}>
                            + 创建数据源
                          </Button>
                        </div>
                      )}
                    />
                  </Form.Item>
                  <Button
                    className="ml-[8px]"
                    onClick={() => loadConnectionList(sourceForm.getFieldValue('sourceType'))}
                  >
                    <IconSvg type="refresh" />
                  </Button>
                </Form.Item>

                <Form.Item
                  label={
                    <div>
                      <span>文件路径</span>
                      <Tooltip title="指定读取文件所在的目录路径，例如/home/<USER>">
                        <IconSvg
                          type="info"
                          size={18}
                          color="#84868C"
                          fill="white"
                          className="cursor-pointer"
                        />
                      </Tooltip>
                    </div>
                  }
                  name="sourceFilePath"
                  rules={[{required: true, message: '请输入文件路径'}]}
                >
                  <Input placeholder="请输入文件路径" />
                </Form.Item>

                <Form.Item
                  label={
                    <div>
                      <span>路径正则过滤</span>
                      <Tooltip title="使用正则表达式对文件路径进行过滤，例如abc.*匹配以abc开头的文件。可与文件格式过滤配合使用，同时支持配置系统参数变量${logicTime}实现源端路径带有时间字段的增量同步。">
                        <IconSvg
                          type="info"
                          size={18}
                          color="#84868C"
                          fill="white"
                          className="cursor-pointer"
                        />
                      </Tooltip>
                    </div>
                  }
                  name="sourceFileFilterPattern"
                >
                  <Input placeholder="请输入路径正则过滤" />
                </Form.Item>

                <Form.Item
                  label={
                    <div>
                      <span>文件格式过滤</span>
                      <Tooltip title="用于过滤特定扩展名的文件，例如.png，仅支持一个扩展名。如需多个请用路径正则过滤">
                        <IconSvg
                          type="info"
                          size={18}
                          color="#84868C"
                          fill="white"
                          className="cursor-pointer77"
                        />
                      </Tooltip>
                    </div>
                  }
                  name="sourceFileNameExtension"
                  rules={[
                    {
                      pattern: /^\.\w+$/,
                      message: '必须以.开头，例如 .png'
                    }
                  ]}
                >
                  <Input placeholder="请输入文件格式过滤，例如 .png" style={{width: '100%'}} />
                </Form.Item>
                <Form.Item
                  label={
                    <div>
                      <span>更新时间过滤</span>
                      <Tooltip title="用于采集指定时间范围内更新的文件。时间过滤为左闭右开区间：开始时间 ≤ 更新时间 ＜ 结束时间。若结束时间早于开始时间，将导致过滤数据为空。支持固定时间或使用${logicTime}动态参数（格式需为yyyy-MM-dd HH:mm:ss），如${logicTime(yyyy-MM-dd HH:mm:ss,-1d)}表示调度时间前一天。">
                        <IconSvg
                          type="info"
                          size={18}
                          color="#84868C"
                          fill="white"
                          className="cursor-pointer77"
                        />
                      </Tooltip>
                    </div>
                  }
                  name="enableSourceFileFilterMod"
                >
                  <Select
                    placeholder="请选择时间过滤策略"
                    options={EnableSourceFileFilterModOptions}
                    style={{width: '100%'}}
                    defaultValue="disabled"
                    onChange={(value) => {
                      // 当切换为禁用时，清空相关字段的值
                      if (value === SourceFileFilterModStatus.Disabled) {
                        sourceForm.setFieldsValue({
                          sourceFileFilterModMin: undefined,
                          sourceFileFilterModMax: undefined
                        });
                      }
                    }}
                  />
                </Form.Item>

                {/* 根据所选时间过滤策略显示筛选时间输入框 */}
                <Form.Item
                  noStyle
                  dependencies={[
                    'enableSourceFileFilterMod',
                    'sourceFileFilterModMin',
                    'sourceFileFilterModMax'
                  ]}
                >
                  {({getFieldValue}) => {
                    const isEnabled =
                      getFieldValue('enableSourceFileFilterMod') === SourceFileFilterModStatus.Enabled;

                    if (!isEnabled) return null;

                    const minTimeValue = getFieldValue('sourceFileFilterModMin');
                    const maxTimeValue = getFieldValue('sourceFileFilterModMax');

                    // 当开始时间选择了"不限"时，禁用结束时间的"不限"选项
                    const minDisabledTypes =
                      maxTimeValue?.type === TimeFilterType.Unlimited ? [TimeFilterType.Unlimited] : [];
                    // 当结束时间选择了"不限"时，禁用开始时间的"不限"选项
                    const maxDisabledTypes =
                      minTimeValue?.type === TimeFilterType.Unlimited ? [TimeFilterType.Unlimited] : [];

                    return (
                      <>
                        <Form.Item
                          label="开始时间"
                          name="sourceFileFilterModMin"
                          rules={[
                            {
                              validator: createTimeFilterValidator('开始时间')
                            }
                          ]}
                          className="time-filter-input"
                        >
                          <TimeFilterInput disabledTypes={minDisabledTypes} fieldLabel="开始时间" />
                        </Form.Item>
                        <Form.Item
                          label="结束时间"
                          name="sourceFileFilterModMax"
                          className="time-filter-input"
                          rules={[
                            {
                              validator: createTimeFilterValidator('结束时间')
                            }
                          ]}
                        >
                          <TimeFilterInput disabledTypes={maxDisabledTypes} fieldLabel="结束时间" />
                        </Form.Item>
                      </>
                    );
                  }}
                </Form.Item>
              </Form>
            </div>

            <div className="mr-[16px] ml-[16px] mt-[66px]">
              <IconSvg type="bold-right" color="#D4D6D9" size={24} />
            </div>

            <div className={cx('form-box')}>
              <div className={cx('form-box-title')}>目标端配置</div>
              <Form
                form={destForm}
                labelAlign="left"
                labelWidth="96px"
                colon={false}
                className={cx('form-box-content')}
                initialValues={{
                  sinkType: SinkType.Catalog,
                  duplicateFileStrategy: DuplicateFileStrategyType.Overwrite
                }}
              >
                <Form.Item
                  label="目标端类型"
                  name="sinkType"
                  rules={[{required: true, message: '请选择目标端类型'}]}
                >
                  <Select options={DestOptions} disabled style={{width: 434.8}} />
                </Form.Item>
                <Form.Item
                  label="目标端路径"
                  name="sinkVolume"
                  rules={[{required: true, message: '请输入目标端路径'}]}
                  style={{marginBottom: '0'}}
                >
                  <FilePathSelect
                    form={destForm}
                    metaDirs={['volume']}
                    showPathSelect
                    selectableLevel={PathLevel.Volume}
                    hasDoris={false}
                  />
                </Form.Item>
                <Form.Item
                  label={
                    <div>
                      <span>同名文件处理</span>
                      <Tooltip title="支持配置同名文件处理策略">
                        <IconSvg
                          type="info"
                          size={18}
                          color="#84868C"
                          fill="white"
                          className="cursor-pointer77"
                        />
                      </Tooltip>
                    </div>
                  }
                  name="duplicateFileStrategy"
                  extra={(() => {
                    const duplicateFileStrategyOption = DuplicateFileStrategyOptions.find(
                      (item) => item.value === duplicateFileStrategy
                    );
                    return duplicateFileStrategyOption?.describe || '';
                  })()}
                >
                  <Select options={DuplicateFileStrategyOptions} style={{width: 434.8}} />
                </Form.Item>
              </Form>
            </div>
          </div>

          <div className={cx('group-title')}>运行配置</div>

          <Form.Item label="计算实例" name="compute" rules={[{required: true, message: '请选择计算实例'}]}>
            <Select
              placeholder="请选择计算实例"
              loading={computedList.loading}
              options={computedList.options}
              style={{width: 428}}
            />
          </Form.Item>

          <Form.Item
            label={
              <div>
                <span>最大并发数</span>
                <Tooltip title="同时从源端并行读取和向目的端并行写入的最大线程数，建议根据数据量和资源情况合理调整">
                  <IconSvg type="info" size={18} color="#84868C" fill="white" className="cursor-pointer" />
                </Tooltip>
              </div>
            }
            name="parallelism"
            rules={[{required: true, message: '请输入最大并发数'}]}
          >
            <InputNumber min={1} max={6} width={200} />
          </Form.Item>
        </Form>
      </div>

      <div className={cx('footer')}>
        <Button
          type="primary"
          htmlType="submit"
          onClick={submit(true)}
          size="large"
          loading={saving}
          style={{width: 160}}
        >
          保存并前置检查
        </Button>
        <Button htmlType="submit" onClick={submit()} size="large" loading={saving}>
          保存
        </Button>
        <Button htmlType="submit" onClick={onCancel} size="large" disabled={saving}>
          取消
        </Button>
      </div>
    </div>
  );
};

export default FileCollectCreate;

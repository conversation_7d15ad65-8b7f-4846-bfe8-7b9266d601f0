import {<PERSON><PERSON>rder<PERSON>y, JobType, RunStats, FileSourceType, Job, JobConfigStatus} from '@api/integration/type';
import urls from '@utils/urls';
import {Button, Dropdown, Link, Menu, Popover, Search, Space, Table, Tag} from 'acud';
import React, {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {useMemoizedFn} from 'ahooks';
import {useNavigate} from 'react-router-dom';
import {
  DataSourceMap,
  JobStatusConfig,
  Operation,
  OperationShowType,
  orderMap,
  JobStatusConfigMap
} from '../constants';
import {IntegrationTab} from '@pages/DataIntegration/constants';

import styles from './index.module.less';
import classNames from 'classnames/bind';
import {Order} from '@api/common';
import * as http from '@api/integration';
import {WorkspaceContext} from '@pages/index';
import IconSvg from '@components/IconSvg';
import {getMetaUrl, getO<PERSON>ation<PERSON>ist, JobItem, onDelete} from '../utils';
import TextEllipsis from '@components/TextEllipsisTooltip';
import moment from 'moment';
import AuthButton from '@components/AuthComponents/AuthButton';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege, ResourceType} from '@api/permission/type';
import {onStart} from '../FileColletRunModal';
import {TooltipType} from '@components/AuthComponents/constants';
import PermissionModal from '@components/Workspace/PermissionModal';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import {Eye} from '@baidu/xicon-react-bigdata';
import {ColumnsType} from 'acud/lib/table';
import {FixedType} from 'acud/lib/table/baseTable/interface';
import SearchFormItem from '../components/SearchFormItem';
import OnOpenModal, {ModalTypeEnum} from '../../SqlCollect/OfflineCollect/components/OnOpenModal';
import PreCheckModal from '../../SqlCollect/OfflineCollect/components/PreCheckModal';

export const PRIVILEGE_LIST = [
  {label: '查看', value: 'VIEW'},
  {label: '运行', value: 'EXECUTE'},
  {label: '管理', value: 'MANAGE'}
];

const cx = classNames.bind(styles);

interface FileCollectListProps {
  createModal?: () => void; // 创建文件采集弹窗
}

/**
 * 文件采集-列表
 */
export const FileCollectList: React.FC<FileCollectListProps> = ({createModal}) => {
  const navigate = useNavigate();

  const permissionModalRef = useRef<any>(null);

  const permission = useWorkspaceAuth([Privilege.UnstructuredIntegrationCreate]);
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);

  // 搜索对象状态
  const [searchObj, setSearchObj] = useState({
    keyword: 'namePattern',
    value: ''
  });

  const [total, setTotal] = useState(0);
  const [jobList, setJobList] = useState<JobItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [sourceType, setSourceType] = useState<FileSourceType>();
  const [jobConfigStatus, setJobConfigStatus] = useState<JobConfigStatus>();
  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({pageNo: 1, pageSize: 10});
  const [sorter, setSorter] = useState<{field: JobOrderBy; order: Order}>({
    field: JobOrderBy.CreateTime,
    order: Order.Desc
  });
  const [selectedRows, setSelectedRows] = useState<JobItem[]>([]);

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null); // 用于存储当前选中的任务，供 PreCheckModal 组件使用

  // 获取任务列表
  const getJobList = useCallback(async () => {
    setSelectedRows([]);
    const params = {
      sourceType: sourceType,
      jobStatusFilter: jobConfigStatus,
      [searchObj.keyword]: searchObj.value,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      orderBy: sorter.field,
      order: orderMap[sorter.order],
      type: JobType.File
    };
    setLoading(true);
    try {
      const res = await http.getIntegrationJobList(workspaceId, params);
      const {totalCount, jobs} = res.result;
      setTotal(totalCount);
      setJobList(
        jobs.map((item) => ({
          ...item,
          operations: getOperationList(navigate, {
            status: item.status,
            privileges: item?.privileges || []
          }),
          draft: item.draft
            ? {
                ...item.draft,
                operations: getOperationList(navigate, {
                  status: item.draft.status,
                  privileges: item?.privileges || []
                })
              }
            : undefined
        }))
      );
    } catch (error) {
      console.error('获取文件采集任务列表失败', error);
    } finally {
      setLoading(false);
    }
  }, [
    navigate,
    pagination.pageNo,
    pagination.pageSize,
    searchObj,
    sorter.field,
    sorter.order,
    sourceType,
    jobConfigStatus,
    workspaceId
  ]);

  useEffect(() => {
    getJobList();
  }, [getJobList]);

  // 渲染最近运行状态
  const renderLatestRunStats = (latestRunStats: RunStats[]) => {
    if (!latestRunStats?.length) {
      return '-';
    }
    return (
      <div className={cx('status-wrapper')}>
        {latestRunStats?.map((item, index) => {
          const {runId, startTime, status} = item;
          const config = JobStatusConfig[status];
          const contentFields = [
            {label: '运行ID', value: runId},
            {label: '运行时间', value: moment(startTime).format('YYYY-MM-DD HH:mm:ss')},
            {label: '运行状态', value: config?.label}
          ];
          const content = contentFields.map((field, index) => (
            <div key={index}>
              <span>{field.label}：</span>
              <span>{field.value}</span>
            </div>
          ));
          return (
            <Popover key={index} placement="bottom" content={content}>
              <div className={cx('status', `status-${config?.icon}`)}></div>
            </Popover>
          );
        })}
      </div>
    );
  };

  // 渲染操作项
  const renderOperation = (record: JobItem) => {
    const operations = record.operations;
    const menu = (
      <Menu>
        {operations[OperationShowType.Dropdown].map((item, index) => (
          <AuthMenuItem
            key={index}
            onClick={() => item.callback([record], workspaceId, getJobList)}
            isAuth={item.isAuth}
            disabled={item.disabled}
          >
            {item.label}
          </AuthMenuItem>
        ))}
        <AuthMenuItem
          isAuth={record.privileges.includes(Privilege.Manage)}
          onClick={() =>
            permissionModalRef.current?.open({
              resourceType: ResourceType.StructuredIntegration,
              resourceId: record.jobId,
              resourceName: record.name,
              privilegeList: PRIVILEGE_LIST
            })
          }
        >
          权限管理
        </AuthMenuItem>
      </Menu>
    );
    return (
      <Space className={cx('operation')}>
        {operations[OperationShowType.List].map((item, index) => (
          <AuthButton
            type="actiontext"
            key={index}
            onClick={() => item.callback([record], workspaceId, getJobList)}
            isAuth={item.isAuth}
            disabled={item.disabled}
          >
            {item.label}
          </AuthButton>
        ))}
        <Dropdown overlay={menu}>
          <IconSvg type="more" size={16} color="#6c6d70" />
        </Dropdown>
      </Space>
    );
  };

  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
      render: (text, record) => {
        const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(record.status);
        return (
          <TextEllipsis tooltip={text}>
            <Link
              onClick={() =>
                navigate(`${urls.fileCollectDetail}?jobId=${record.jobId}&isPublished=${isPublished}`)
              }
            >
              {text}
            </Link>
          </TextEllipsis>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 150,
      filters: Object.keys(JobStatusConfigMap).map((item) => ({
        text: JobStatusConfigMap[item].label,
        value: item
      })),
      filterMultiple: false,
      render: (status, record) => {
        const validStatuses = [
          JobConfigStatus.PreCheck,
          JobConfigStatus.CheckPass,
          JobConfigStatus.CheckFail
        ];
        return (
          <Tag
            color="transparent"
            icon={<span className={`circle ${JobStatusConfigMap[status]?.icon}`}></span>}
          >
            {JobStatusConfigMap[status]?.label}
            {validStatuses.includes(status) && (
              <Button
                icon={<Eye theme="line" color="#000000" size={16} strokeLinejoin="round" />}
                onClick={() => {
                  setSelectedJob(record);
                  setModalVisible(true);
                }}
                type="actiontext"
              />
            )}
          </Tag>
        );
      }
    },
    {
      title: '源端类型',
      dataIndex: 'sourceType',
      filters: [
        {text: FileSourceType.FTP, value: FileSourceType.FTP},
        {text: FileSourceType.SFTP, value: FileSourceType.SFTP},
        {text: FileSourceType.HDFS, value: FileSourceType.HDFS}
      ],
      width: 110,
      filterMultiple: false,
      render: (text) => (
        <div className={cx('source-type')}>
          <IconSvg
            type={DataSourceMap[text]?.icon}
            color={DataSourceMap[text]?.color}
            className="bordered-circle-icon mr-[8px]"
            size={14}
          />
          {text}
        </div>
      )
    },
    {
      title: '源端数据源',
      dataIndex: 'sourceConnectionId',
      width: 120,
      // 点击新开页签至数据源页面，根据数据源名称进行检索
      render: (text, record) => (
        <TextEllipsis tooltip={text} width={120}>
          <Link onClick={() => navigate(`${urls.connectionDetail}?name=${text}&workspaceId=${workspaceId}`)}>
            {text}
          </Link>
        </TextEllipsis>
      )
    },
    {
      title: '目标端路径',
      dataIndex: 'sinkVolume',
      width: 230,
      ellipsis: true,
      // 点击新开页签至当前路径对应的元数据列表
      render: (text) => (
        <TextEllipsis tooltip={text}>
          <Link onClick={() => navigate(getMetaUrl(text, workspaceId))}>{text}</Link>
        </TextEllipsis>
      )
    },
    {
      title: '最近运行',
      dataIndex: 'latestRunStats',
      render: (text) => renderLatestRunStats(text),
      width: 160
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 100,
      render: (text) => (
        <TextEllipsis tooltip={text} width={100}>
          {text}
        </TextEllipsis>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      sorter: true,
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 160
    },
    {
      title: '操作',
      fixed: 'right',
      width: 220,
      dataIndex: 'operation',
      render: (_, record) => renderOperation(record)
    }
  ] as ColumnsType<JobItem>;

  // 子表格的列定义
  const subColumns: ColumnsType<JobItem> = columns.map((col) => {
    const {filters, sorter, filterMultiple, ...rest} = col;
    return {...rest};
  });

  const onPageChange = (page, pageSize) => {
    setPagination({
      pageNo: page,
      pageSize: pageSize
    });
  };

  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSorter({field: sorter.field, order: sorter.order});
    setSourceType(filters.sourceType?.[0] as FileSourceType);
    setJobConfigStatus(filters.status?.[0] as JobConfigStatus);
  }, []);

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys: selectedRows.map((item: JobItem) => item.jobId),
      onChange: (_, selectedRows: JobItem[]) => {
        setSelectedRows(selectedRows);
      }
    }),
    [selectedRows]
  );

  // 获取批量操作按钮的禁用状态
  const getDisableStatus = useCallback(
    (type: Operation) => {
      if (selectedRows.length === 0) return true;
      const operations = selectedRows.map((item) =>
        [...item.operations[OperationShowType.List], ...item.operations[OperationShowType.Dropdown]].filter(
          (item) => item.isAuth && !item.disabled
        )
      );
      return !operations.every((list) => {
        return list.find((item) => item.key === type);
      });
    },
    [selectedRows]
  );

  const searchFn = useMemoizedFn(() => {
    setPagination((pre) => ({...pre, pageNo: 1}));
  });

  const handleSearchObjChange = (obj: {keyword: string; value: string}) => {
    setSearchObj(obj);
  };

  return (
    <div>
      <div className={cx('header')}>
        <SearchFormItem onChange={handleSearchObjChange} searchObj={searchObj} searchFn={searchFn} />

        <div className={cx('header-right')}>
          <Button onClick={getJobList} className="mr-[10px]">
            <IconSvg type="refresh" size={16} color="#6c6d70" />
          </Button>
          <Button
            onClick={() =>
              OnOpenModal(
                ModalTypeEnum.StartJob,
                selectedRows,
                workspaceId,
                () => {
                  getJobList();
                },
                navigate,
                JobType.File
              )
            }
            disabled={getDisableStatus(Operation.Start)}
            className="mr-[10px]"
          >
            运行
          </Button>
          <Button
            onClick={() => onDelete(selectedRows, workspaceId, getJobList)}
            disabled={getDisableStatus(Operation.Delete)}
            className="mr-[10px]"
          >
            删除
          </Button>
          <AuthButton
            tooltipType={TooltipType.Function}
            isAuth={permission[Privilege.UnstructuredIntegrationCreate]}
            type="primary"
            onClick={createModal}
            className="mr-[10px]"
          >
            + 创建
          </AuthButton>
          <div>已经选择{rowSelection?.selectedRowKeys?.length ?? 0}项</div>
        </div>
      </div>

      <Table
        scroll={{x: 1200}}
        columns={columns}
        dataSource={jobList}
        loading={loading}
        pagination={{
          current: pagination.pageNo,
          pageSize: pagination.pageSize,
          total: total,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          showTotal: (total) => `共${total}条`,
          onChange: onPageChange
        }}
        onChange={handleTableChange}
        rowKey="jobId"
        rowSelection={{
          type: 'checkbox',
          ...rowSelection
        }}
        expandable={{
          expandedRowRender: (record) => {
            const {draft, privileges} = record;
            if (!record?.draft) {
              return null;
            }

            const dataSource = [
              {
                ...draft,
                privileges,
                operations: getOperationList(navigate, {
                  status: draft.status,
                  privileges: privileges || []
                })
              }
            ] as JobItem[];

            return (
              <Table
                scroll={{x: 1200}}
                columns={subColumns}
                dataSource={dataSource}
                loading={loading}
                pagination={false}
              />
            );
          },
          rowExpandable: (record) => record.status === JobConfigStatus.Updating
        }}
      />

      <PermissionModal workspaceId={workspaceId} ref={permissionModalRef} onSuccess={getJobList} />

      {modalVisible && (
        <PreCheckModal
          jobs={[selectedJob]}
          workspaceId={workspaceId}
          afterClose={() => setModalVisible(false)}
          isPreCheck={false}
          type={IntegrationTab.FileCollect}
        />
      )}
    </div>
  );
};

.info {
  &-container {
    background-color: #fff;
    overflow-y: auto;
    margin: 8px;
    border-radius: 6px;
    border: 1px solid #d4d6d9;
    padding: 16px;
    width: 100%;
  }

  &-top {
    margin: 16px 0;
    display: flex;
    justify-content: space-between;
  }

  &-bottom {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  &-title {
    font-weight: 500;
    font-size: 22px;
    line-height: 32px;
    color: #151b26;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-text {
    color: #84868c;
    line-height: 13px;
    height: 13px;
    flex-shrink: 0;
    padding-left: 8px;
    margin-left: 8px;
    border-left: 1px solid #d4d6d9;
    display: flex;
    align-items: center;

    &-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      display: inline-block;
    }
  }
  &-status {
    border-radius: 10px;
    background-color: #f7f7f9;
    padding: 4px 10px;
    font-weight: 500;
    font-size: 12px;
    height: 24px;
    box-sizing: border-box;
    margin-right: 64px;
    white-space: nowrap;
  }
}

.source-name,
.dest-name {
  height: 20px;
  line-height: 20px;
  border-radius: 22px;
  background-color: #f3f3f6;
  color: #151b26;
  padding: 0 6px;
  display: flex;
  align-items: center;
}

.dest-name {
  width: 132px;
}

import React, {useMemo} from 'react';

import styles from './index.module.less';
import {JobDetailRes, DuplicateFileStrategyType} from '@api/integration/type';

interface ProcessLogDetailConfigProps {
  detail: JobDetailRes;
}

const sourceFileFilterModText = (min: string, max: string) => {
  if (!min && !max) {
    return '不过滤';
  }
  return `${min || '-'} 至 ${max || '-'}`;
};

const duplicateFileStrategyMap = {
  [DuplicateFileStrategyType.Overwrite]: '覆盖',
  [DuplicateFileStrategyType.Rename]: '重命名',
  [DuplicateFileStrategyType.Skip]: '跳过'
};

/**
 * 文件采集详情-任务配置
 */
const FileCollectDetailConfig: React.FC<ProcessLogDetailConfigProps> = ({detail}) => {
  const config = useMemo(
    () => [
      {
        title: '源端信息',
        fields: [
          {
            title: '数据源名称',
            value: detail?.sourceConfig?.sourceConnectionId
          },
          {
            title: '文件路径',
            value: detail?.sourceConfig?.sourceFilePath
          },
          {
            title: '路径正则过滤',
            value: detail?.sourceConfig?.sourceFileFilterPattern
          },
          {
            title: '文件格式过滤',
            value: detail?.sourceConfig?.sourceFileNameExtension
          },
          {
            title: '更新时间过滤',
            value: sourceFileFilterModText(
              detail?.sourceConfig?.sourceFileFilterModMin,
              detail?.sourceConfig?.sourceFileFilterModMax
            )
          }
        ]
      },
      {
        title: '目标端信息',
        fields: [
          {
            title: '目标端路径',
            value: detail?.sinkConfig?.sinkVolume
          },
          {
            title: '同名文件处理',
            value: duplicateFileStrategyMap[detail?.sinkConfig?.duplicateFileStrategy]
          }
        ]
      },
      {
        title: '运行信息',
        fields: [
          {
            title: '计算实例',
            value: detail?.compute?.name
          },
          {
            title: '最大并发数',
            value: detail?.sourceConfig?.parallelism
          }
        ]
      }
    ],
    [detail]
  );
  return (
    <div>
      {config.map((module, index) => (
        <div className={styles['module-container']} key={index}>
          <div className={styles['module-title']}>
            <span>{module.title}</span>
            <span className={styles['sum']}>共{module.fields.length}条</span>
          </div>
          <div className={styles['module-content']}>
            {module.fields.map((item, index) => (
              <div className={styles['fields-item']} key={index}>
                <div className={styles['fields-title']}>{item.title}</div>
                <div className={styles['fields-value']}>{item.value || '-'}</div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default FileCollectDetailConfig;

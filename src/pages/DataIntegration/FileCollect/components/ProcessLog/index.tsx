import React, {forwardRef, useCallback, useContext, useEffect, useImperativeHandle, useState} from 'react';
import {WorkspaceContext} from '@pages/index';
import {Button, Link, Space, Table} from 'acud';
import {ExecutionInfo, ExecutionOrderBy, InstanceStatus, FileJobRunType} from '@api/integration/type';
import useUrlState from '@ahooksjs/use-url-state';
import {Order} from '@api/common';
import * as http from '@api/integration';
import moment from 'moment';
import {FileJobRunTypeConfig, InstanceStatusConfig, orderMap} from '../../constants';
import InstanceLogDrawer from '../InstanceLogDrawer';

import styles from './index.module.less';
import {onStop} from '../../utils';
import IconSvg from '@components/IconSvg';
import classNames from 'classnames/bind';
import AuthButton from '@components/AuthComponents/AuthButton';
import {Privilege} from '@api/permission/type';

const cx = classNames.bind(styles);

/**
 * 文件采集详情-运行记录
 */
const ProcessLog = forwardRef((props: {privilege: Privilege[]}, ref) => {
  const {privilege} = props;
  const [urlState] = useUrlState({jobId: '', isPublished: false});
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  const [total, setTotal] = useState<number>(0);
  const [status, setStatus] = useState<InstanceStatus>();
  const [triggerType, setTriggerType] = useState<FileJobRunType>();
  const [executionList, setExecutionList] = useState<ExecutionInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({pageNo: 1, pageSize: 10});
  const [sorter, setSorter] = useState<{field: ExecutionOrderBy; order: Order}>();
  const [logDrawerVisible, setLogDrawerVisible] = useState<boolean>(false);
  const [logRunId, setLogRunId] = useState<string>();

  useImperativeHandle(ref, () => ({
    refresh: getJobList
  }));

  // 获取任务列表
  const getJobList = useCallback(async () => {
    const params = {
      status,
      triggerType,
      isPublished: urlState.isPublished,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      orderBy: sorter?.order ? sorter?.field : undefined,
      order: orderMap[sorter?.order]
    };
    setLoading(true);
    try {
      const res = await http.listExecutions(workspaceId, urlState.jobId, params);
      const {totalCount, executions} = res.result;
      setTotal(totalCount);
      setExecutionList(
        executions.map((item) => {
          const duration =
            item.duration === -1 && item.executedTime
              ? Math.floor((Date.now() - new Date(item.executedTime).getTime()) / 1000)
              : item.duration;
          return {...item, duration};
        })
      );
    } catch {
      console.error('获取文件采集任务列表失败');
    } finally {
      setLoading(false);
    }
  }, [
    pagination.pageNo,
    pagination.pageSize,
    sorter?.field,
    sorter?.order,
    status,
    triggerType,
    urlState.jobId,
    urlState.isPublished,
    workspaceId
  ]);

  useEffect(() => {
    getJobList();
  }, [getJobList]);

  // 日志弹窗回调
  const onLogDrawer = (runId) => () => {
    setLogDrawerVisible(true);
    setLogRunId(runId);
  };

  const onLogDrawerClose = () => setLogDrawerVisible(false);

  const onExecutionStop = useCallback(
    (runId) => () => {
      onStop(urlState.jobId, workspaceId, runId, getJobList);
    },
    [getJobList, urlState.jobId, workspaceId]
  );

  // 渲染操作项
  const renderOperation = (record: ExecutionInfo) => {
    return (
      <Space>
        <Button type="actiontext" onClick={onLogDrawer(record.runId)}>
          查看日志
        </Button>
        {record.status === InstanceStatus.Running ? (
          <AuthButton
            isAuth={privilege?.includes(Privilege.Manage)}
            type="actiontext"
            onClick={onExecutionStop(record.runId)}
          >
            终止
          </AuthButton>
        ) : null}
      </Space>
    );
  };

  const columns = [
    {
      title: '运行记录 ID',
      dataIndex: 'runId'
    },
    {
      title: '业务时间',
      dataIndex: 'scheduleTime',
      sorter: true,
      render: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '状态',
      dataIndex: 'status',
      filters: Object.keys(InstanceStatusConfig).map((key) => ({
        text: InstanceStatusConfig[key].label,
        value: key
      })),
      filterMultiple: false,
      render: (key) => {
        const info = InstanceStatusConfig[key];
        return (
          <div className={styles['status-tag']} style={{color: info?.textColor, background: info?.color}}>
            {info?.label}
          </div>
        );
      }
    },
    {
      title: '运行类型',
      dataIndex: 'triggerType',
      filters: Object.keys(FileJobRunTypeConfig).map((key) => ({
        text: FileJobRunTypeConfig[key].label,
        value: key
      })),
      filterMultiple: false,
      render: (text) => FileJobRunTypeConfig[text]?.label || '-'
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      sorter: true,
      render: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      sorter: true,
      render: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '运行时长',
      dataIndex: 'duration',
      render: (text) => {
        if (text === -1) {
          return '-';
        }
        return `${text}s`;
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      render: (_, record) => renderOperation(record)
    }
  ];

  const onPageChange = (page, pageSize) => {
    setPagination({
      pageNo: page,
      pageSize: pageSize
    });
  };

  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSorter({field: sorter?.field, order: sorter?.order});
    setStatus(filters.status?.[0] as InstanceStatus);
    setTriggerType(filters.triggerType?.[0] as FileJobRunType);
  }, []);

  return (
    <div>
      <Button onClick={getJobList} className={cx('mr-[10px]', 'mb-[12px]', 'refresh-button')}>
        <IconSvg type="refresh" size={16} color="#6c6d70" />
      </Button>
      <Table
        columns={columns}
        dataSource={executionList}
        loading={loading}
        pagination={{
          current: pagination.pageNo,
          pageSize: pagination.pageSize,
          total,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          showTotal: (total) => `共${total}条`,
          onChange: onPageChange
        }}
        onChange={handleTableChange}
      />
      <InstanceLogDrawer onClose={onLogDrawerClose} runId={logRunId} visible={logDrawerVisible} />
    </div>
  );
});

ProcessLog.displayName = 'ProcessLog';

export default ProcessLog;

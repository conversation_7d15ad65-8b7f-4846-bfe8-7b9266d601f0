import {JobType} from '@api/integration/type';
import FilePathSelect, {MetaType, PathLevel} from '@components/FilePathSelect';
import PaySelect from '@pages/Compute/components/PaySelect';
import {
  SinkNameRuleChineseMap,
  SinkNameRuleEnum,
  SinkTableTypeChineseMap,
  SinkTableTypeEnum,
  SinkTypeEnum,
  SourceConfigEnum,
  TargetTypeEnum
} from '@pages/DataIntegration/SqlCollect/constants';
import {RULE} from '@utils/regs';
import {Button, Form, Input, Link, Select, toast} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React, {useContext, useEffect, useState} from 'react';
import CardTwoContent from '../../components/CardTwoContent';
import ComputeSelect from '../../components/ComputeSelect';
import IsAutoCreatedSelect from '../../components/isAutoCreatedSelect';
import TableSelectFormItem from '../../components/TableSelectFormItem';
import styles from './index.module.less';
import {useNavigate} from 'react-router-dom';
import {getMetaUrlTable} from '@pages/DataIntegration/SqlCollect/utils';
import {WorkspaceContext} from '@pages/index';
import Space from 'acud/lib/table/Space';
import {useMemoizedFn} from 'ahooks';
import {EntityType} from '@api/metaRequest';
import FilePathSelectFormItem from '@components/FilePathSelectFormItem';
import {Privilege, ResourceType} from '@api/permission/type';

const {Option} = Select;

const SourceSink: React.FC<{form: FormInstance; isEdit: boolean; currentStep: number}> = ({
  form,
  isEdit,
  currentStep
}) => {
  const isAutoCreated = Form.useWatch([SourceConfigEnum.SinkConfig, 'isAutoCreated'], form);
  const sinkType = Form.useWatch([SourceConfigEnum.SinkConfig, 'sinkType'], form);
  const navigate = useNavigate();
  const [closeModalTime, setCloseModalTime] = useState(0);
  const {workspaceId} = useContext(WorkspaceContext);

  useEffect(() => {
    setCloseModalTime(new Date().getTime());
  }, [currentStep]);

  // 清空表名
  const clearTableName = useMemoizedFn(() => {
    form.setFieldValue('sinkFullName', '');
  });
  // 清空映射
  const clearMapping = useMemoizedFn(() => {
    form.setFieldValue([SourceConfigEnum.MappingConfig, 'sinkFields'], []);
    form.setFieldValue([SourceConfigEnum.MappingConfig, 'mapping'], []);
    form.setFieldValue([SourceConfigEnum.MappingConfig, 'sinkPartitions'], []);
    form.setFieldValue([SourceConfigEnum.SinkConfig, 'sinkPath'], '');
    form.setFieldValue('sinkFullName', '');

    // 切换建表方式 初始化 表类型 和 表名设置
    form.setFieldValue(
      [SourceConfigEnum.SinkConfig, 'sinkTableType'],
      sinkType === SinkTypeEnum.doris ? '' : SinkTableTypeEnum.MANAGED
    );
    form.setFieldValue([SourceConfigEnum.SinkConfig, 'sinkNameRule'], SinkNameRuleEnum.SAME);
  });
  const left = () => {
    return (
      <>
        <TableSelectFormItem form={form} isEdit={isEdit} />
      </>
    );
  };

  const right = () => {
    return (
      <>
        <Form.Item label={'数据去向'} name={[SourceConfigEnum.SinkConfig, 'target']} noStyle hidden>
          <Select placeholder="请选择数据去向" disabled>
            {Object.values(TargetTypeEnum).map((item) => (
              <Option key={item} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={'目标端类型'} name={[SourceConfigEnum.SinkConfig, 'sinkType']}>
          <Select placeholder="请选择目标端类型">
            {Object.values(SinkTypeEnum).map((item) => (
              <Option key={item} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={'建表方式'} name={[SourceConfigEnum.SinkConfig, 'isAutoCreated']}>
          <IsAutoCreatedSelect onChange={clearMapping} />
        </Form.Item>

        {isAutoCreated && (
          <>
            {sinkType !== SinkTypeEnum.doris ? (
              <Form.Item label={'表类型'} name={[SourceConfigEnum.SinkConfig, 'sinkTableType']}>
                <Select placeholder="请选择表类型">
                  {/* {Object.values(SinkTableTypeEnum).map((item) => (
                  <Option key={item} value={item}>
                    {SinkTableTypeChineseMap[item]}
                  </Option>
                ))} */}
                  <Option value={SinkTableTypeEnum.MANAGED}>
                    {SinkTableTypeChineseMap[SinkTableTypeEnum.MANAGED]}
                  </Option>
                </Select>
              </Form.Item>
            ) : null}

            <Form.Item label={'表名设置'} name={[SourceConfigEnum.SinkConfig, 'sinkNameRule']}>
              <Select placeholder="请选择表类型">
                {Object.values(SinkNameRuleEnum).map((item) => (
                  <Option key={item} value={item}>
                    {SinkNameRuleChineseMap[item]}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              shouldUpdate={(prevValues, currentValues) =>
                prevValues[SourceConfigEnum.SinkConfig]?.sinkNameRule !==
                currentValues[SourceConfigEnum.SinkConfig]?.sinkNameRule
              }
              noStyle
            >
              {({getFieldValue}) => {
                const sinkNameRule = getFieldValue([SourceConfigEnum.SinkConfig, 'sinkNameRule']);

                const isShowPrefix =
                  sinkNameRule === SinkNameRuleEnum.ADDPREFIX ||
                  sinkNameRule === SinkNameRuleEnum.ADDPREFIXANDSUFFIX;
                const isShowSuffix =
                  sinkNameRule === SinkNameRuleEnum.ADDSUFFIX ||
                  sinkNameRule === SinkNameRuleEnum.ADDPREFIXANDSUFFIX;
                return (
                  <>
                    {isShowPrefix && (
                      <Form.Item
                        label={'前缀'}
                        name={[SourceConfigEnum.SinkConfig, 'prefix']}
                        rules={[
                          {required: true, message: '请输入表名前缀'},
                          {
                            pattern: RULE.tableSuffixNameRule,
                            message: RULE.tableSuffixNameRuleText
                          }
                        ]}
                        extra={RULE.tableSuffixNameRuleText}
                      >
                        <Input
                          placeholder="请输入表名前缀"
                          onChange={clearTableName}
                          limitLength={128}
                          forbidIfLimit
                        />
                      </Form.Item>
                    )}
                    {isShowSuffix && (
                      <Form.Item
                        label={'后缀'}
                        name={[SourceConfigEnum.SinkConfig, 'suffix']}
                        rules={[
                          {required: true, message: '请输入表名后缀'},
                          {
                            pattern: RULE.tableSuffixNameRule,
                            message: RULE.tableSuffixNameRuleText
                          }
                        ]}
                        extra={RULE.tableSuffixNameRuleText}
                      >
                        <Input
                          placeholder="请输入表名后缀"
                          onChange={clearTableName}
                          limitLength={128}
                          forbidIfLimit
                        />
                      </Form.Item>
                    )}
                  </>
                );
              }}
            </Form.Item>
          </>
        )}
        <Form.Item
          shouldUpdate={(prevValues, currentValues) =>
            prevValues[SourceConfigEnum.SinkConfig]?.isAutoCreated !==
              currentValues[SourceConfigEnum.SinkConfig]?.isAutoCreated ||
            prevValues.sinkFullName !== currentValues.sinkFullName
          }
          noStyle
        >
          {({getFieldValue}) => {
            const isAutoCreated = getFieldValue([SourceConfigEnum.SinkConfig, 'isAutoCreated']);
            const sinkFullName = getFieldValue('sinkFullName');
            const selectableLevel = isAutoCreated ? PathLevel.Schema : PathLevel.Volume;
            const latestLevel = isAutoCreated ? PathLevel.Catalog : PathLevel.Schema;
            const label = isAutoCreated ? '数据库' : '数据表';
            const schemaPrivilege = isAutoCreated ? [Privilege.CreateTable] : [];
            const tablePrivilege = !isAutoCreated ? [Privilege.WriteTable] : [];
            const placeholder = isAutoCreated ? '<catalog>.<schema>' : '<catalog>.<schema>.<table>';
            const searchType = isAutoCreated
              ? [EntityType.CATALOG, EntityType.SCHEMA]
              : [EntityType.CATALOG, EntityType.SCHEMA, EntityType.TABLE];
            // 编辑任务时， 表已经创建 并且是 自动建表
            const showTableStr = isEdit && !!sinkFullName && isAutoCreated;
            return (
              <>
                <Form.Item label={label} hidden={!showTableStr}>
                  <Space>
                    <Button
                      type="actiontext"
                      onClick={() => {
                        window.open(
                          `${window.location.pathname}#${getMetaUrlTable(sinkFullName, workspaceId)}`,
                          '_blank'
                        );
                      }}
                    >
                      {sinkFullName}
                    </Button>
                  </Space>
                </Form.Item>

                <Form.Item hidden={showTableStr} noStyle>
                  <FilePathSelectFormItem
                    form={form}
                    name={[SourceConfigEnum.SinkConfig, 'sinkPath']}
                    label={label}
                    placeholderReplace={placeholder}
                    onChange={(value) => {
                      // 选择已有表 清空映射
                      if (!isAutoCreated) {
                        clearMapping();
                      }
                    }}
                    searchType={searchType}
                    selectableLevel={selectableLevel}
                    latestLevel={latestLevel}
                    metaDirs={['table']}
                    metaType={MetaType.Table}
                    closeModalTime={closeModalTime}
                    schemaPrivilege={schemaPrivilege}
                    tablePrivilege={tablePrivilege}
                  />
                </Form.Item>
              </>
            );
          }}
        </Form.Item>

        <Form.Item
          label={'描述'}
          name={[SourceConfigEnum.SinkConfig, 'comment']}
          rules={[{max: 256, message: '不超过256字符'}]}
        >
          <Input.TextArea
            placeholder="请输入描述"
            limitLength={256}
            forbidIfLimit
            autoSize={{minRows: 3, maxRows: 6}}
          />
        </Form.Item>
      </>
    );
  };

  return (
    <div className={styles['source-sink']}>
      <div className={styles['form-base']}>
        <div className={'form-item-title'}>基本配置</div>
        <Form.Item
          label="任务名称"
          name="name"
          rules={[
            {required: true, message: '请输入任务名称'},
            {
              pattern: RULE.workflowName,
              message: RULE.workflowNameText
            }
          ]}
          extra={RULE.workflowNameText}
        >
          <Input placeholder="请输入任务名称" limitLength={256} forbidIfLimit />
        </Form.Item>
        <Form.Item label="任务描述" name="description" rules={[{max: 500, message: '不超过500字符'}]}>
          <Input.TextArea
            placeholder="请输入任务描述"
            limitLength={500}
            autoSize={{minRows: 3, maxRows: 6}}
          />
        </Form.Item>
        <div className={'form-item-title'}>计算实例配置</div>
        <Form.Item label={'计算实例'} name="compute" rules={[{required: true, message: '请选择计算实例'}]}>
          <ComputeSelect />
        </Form.Item>
      </div>
      <div className={'form-item-title'}>源端与目标端配置</div>
      <CardTwoContent
        left={left()}
        right={right()}
        title={['源端配置', '目标端配置']}
        showDivider
      ></CardTwoContent>
    </div>
  );
};

export default SourceSink;

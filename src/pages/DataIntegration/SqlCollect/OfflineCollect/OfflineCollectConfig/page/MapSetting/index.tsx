import React, {useEffect, useState} from 'react';

import {BatchObj} from '@api/integration/batch';
import {SourceConfigEnum} from '@pages/DataIntegration/SqlCollect/constants';
import Form, {FormInstance} from 'acud/lib/form';
import styles from '../SourceSink/index.module.less';
import MapSettingTable from './MapSettingTable';
import {useMemoizedFn} from 'ahooks';

// 映射配置
const MapSetting: React.FC<{form: FormInstance<any>; currentStep: number}> = ({form, currentStep}) => {
  const [batchObj, setBatchObj] = useState<BatchObj>();

  const workspaceId = Form.useWatch(['workspaceId'], {form});
  const mappingConfig = Form.useWatch([SourceConfigEnum.MappingConfig], {form});

  useEffect(() => {
    if (workspaceId && currentStep === 2) {
      setBatchObj({...form.getFieldsValue()});
    }
  }, [workspaceId, currentStep]);

  return (
    <div className={styles['map-setting-container']}>
      <div className="form-item-title">映射配置</div>
      {currentStep === 2 && batchObj && (
        <div>
          <MapSettingTable
            batchObj={batchObj}
            isReadOnly={false}
            onChange={(key: string[], value) => {
              form.setFieldValue(key, value);
            }}
          />
        </div>
      )}
      {/* 映射配置 */}
      <Form.Item name={[SourceConfigEnum.MappingConfig, 'mapping']} hidden noStyle />

      {/* where语句 */}
      <Form.Item name={[SourceConfigEnum.MappingConfig, 'filter']} hidden noStyle />

      {/* 目标端列 */}
      <Form.Item name={[SourceConfigEnum.MappingConfig, 'sinkFields']} hidden noStyle />

      {/* 目标端分区 */}
      <Form.Item name={[SourceConfigEnum.MappingConfig, 'sinkPartitions']} hidden noStyle />

      {/* ddl 语句 */}
      <Form.Item name={[SourceConfigEnum.MappingConfig, 'createTableStatement']} hidden noStyle />
    </div>
  );
};

export default MapSetting;

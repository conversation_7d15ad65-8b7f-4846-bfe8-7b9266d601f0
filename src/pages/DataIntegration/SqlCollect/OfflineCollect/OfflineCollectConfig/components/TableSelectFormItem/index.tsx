import {IConnection, queryConnectionList} from '@api/connection';
import {getDatasourceDatabases, getDatasourceTableColumns, getDatasourceTables} from '@api/integration/batch';
import {SourceConfigEnum, SourceTypeEnum} from '@pages/DataIntegration/SqlCollect/constants';
import {WorkspaceContext} from '@pages/index';
import {Form, Link, Select, Space} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {useMemoizedFn, useRequest} from 'ahooks';
import React, {useContext} from 'react';
import {typeMapFn} from '../../page/MapSetting/utils';
import urls from '@utils/urls';
import IconSvg from '@components/IconSvg';
import {Privilege} from '@api/permission/type';

const {Option, OptGroup} = Select;

interface TableSelectFormItemProps {
  form: FormInstance;
  isEdit: boolean;
}

const TableSelectFormItem: React.FC<TableSelectFormItemProps> = ({form, isEdit = false}) => {
  const compute: {computeId: string; name: string} = Form.useWatch('compute', {form});

  const sourceType = Form.useWatch([SourceConfigEnum.SourceConfig, 'sourceType'], {form});
  const sourceConnectionId = Form.useWatch([SourceConfigEnum.SourceConfig, 'sourceConnectionId'], {form});
  const sourceDatabase = Form.useWatch([SourceConfigEnum.SourceConfig, 'sourceDatabase'], {form});
  const sourceSchema = Form.useWatch([SourceConfigEnum.SourceConfig, 'sourceSchema'], {form});
  const {workspaceId} = useContext(WorkspaceContext);
  const shouldShowTableSelector = ['SQLServer', 'PostgreSQL', 'HANA'].includes(sourceType)
    ? sourceSchema
    : sourceDatabase;

  // 获取数据源列表
  const {
    data: connectionList,
    loading: connectionLoading,
    run: connectionRun
  } = useRequest(
    () =>
      queryConnectionList(workspaceId, {pageNo: 1, pageSize: 100, type: sourceType}, Privilege.UseConnection),
    {
      manual: true
    }
  );
  // 获取数据库列表
  const {
    run: databasesRun,
    loading: databasesLoading,
    data: databasesList
  } = useRequest(
    () =>
      getDatasourceDatabases(
        {
          environment: {workspaceId: workspaceId, computeId: compute.computeId},
          datasourceInfo: {connectionId: sourceConnectionId}
        },
        Privilege.WriteTable
      ),
    {
      manual: true
    }
  );
  // 获取表列表
  const {
    run: tablesRun,
    loading: tablesLoading,
    data: tablesList
  } = useRequest(
    () => {
      const sourceSchema = form.getFieldValue([SourceConfigEnum.SourceConfig, 'sourceSchema']);
      const sourceDatabase = form.getFieldValue([SourceConfigEnum.SourceConfig, 'sourceDatabase']);
      const isSQLServerOrPostgreSQL = ['SQLServer', 'PostgreSQL', 'HANA'].includes(sourceType);
      return getDatasourceTables({
        environment: {workspaceId: workspaceId, computeId: compute.computeId},
        datasourceInfo: {
          connectionId: sourceConnectionId,
          database: sourceDatabase,
          ...(isSQLServerOrPostgreSQL ? {schema: sourceSchema} : {})
        }
      });
    },
    {
      manual: true
    }
  );
  /** 选择数据表 */
  const onChangeSourceTable = useMemoizedFn(async (table: string) => {
    // 清空映射
    form.setFieldValue([SourceConfigEnum.SourceConfig, 'parallelism'], 1);
    form.setFieldValue([SourceConfigEnum.SourceConfig, 'splitField'], null);
    form.setFieldValue([SourceConfigEnum.MappingConfig, 'sinkFields'], []);
    form.setFieldValue([SourceConfigEnum.MappingConfig, 'mapping'], []);
    form.setFieldValue([SourceConfigEnum.MappingConfig, 'sinkPartitions'], []);
    form.setFieldValue('sinkFullName', '');
    form.setFieldValue('sourceTable', null);
  });
  return (
    <>
      <Form.Item label="源端类型" name={[SourceConfigEnum.SourceConfig, 'sourceType']}>
        <Select
          keepExpand
          groupSelectorRender={(groupLabel, value) => <>{value}</>}
          placeholder="请选择数据源"
          onChange={() => {
            form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceConnectionId'], null);
            form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceDatabase'], null);
            form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceTable'], null);
          }}
        >
          <OptGroup label="关系型数据库" key="manager">
            {Object.values(SourceTypeEnum).map((item) => (
              <Option key={item} value={item}>
                {item}
              </Option>
            ))}
          </OptGroup>
        </Select>
      </Form.Item>
      <Form.Item
        label={<span>数据源名称</span>}
        name={[SourceConfigEnum.SourceConfig, 'sourceConnectionId']}
        rules={[{required: true, message: '请选择数据源'}]}
      >
        <Select
          loading={connectionLoading}
          placeholder="请选择数据源"
          onDropdownVisibleChange={(open) => {
            if (open) {
              connectionRun();
            }
          }}
          onChange={(value) => {
            const curConnectionList = connectionList?.result?.connections.find(
              (item: IConnection) => item.name === value
            );
            const curSourceType = form.getFieldValue([SourceConfigEnum.SourceConfig, 'sourceType']);
            const isSQLServerOrPostgreSQL = [
              SourceTypeEnum.SQLServer,
              SourceTypeEnum.PostgreSQL,
              SourceTypeEnum.Hana
            ].includes(curSourceType);
            const sourceDatabase = isSQLServerOrPostgreSQL ? curConnectionList.database : undefined;
            form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceDatabase'], sourceDatabase);
            isSQLServerOrPostgreSQL &&
              form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceSchema'], null);
            form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceTable'], null);
          }}
          dropdownRender={(menu) => (
            <>
              {menu}
              <div
                style={{
                  borderTop: '1px solid #e8e9eb',
                  padding: '4px 12px'
                }}
              >
                <Link
                  onClick={() =>
                    window.open(
                      `${window.location.pathname}#${urls.connection}?workspaceId=${workspaceId}&mode=create`,
                      '_blank'
                    )
                  }
                >
                  <Space>
                    <IconSvg type="add" size={16} />
                    创建数据源
                  </Space>
                </Link>
              </div>
            </>
          )}
        >
          {connectionList?.result?.connections.map((item: IConnection) => (
            <Option key={item.id} value={item.name}>
              {item.name}
            </Option>
          ))}
        </Select>
      </Form.Item>

      {/* 必须有数据源 */}
      {/* 如果是SQLServer或PostgreSQL类型，数据库名文本框绑定到 sourceSchema */}
      {sourceConnectionId && ['SQLServer', 'PostgreSQL', 'HANA'].includes(sourceType) && (
        <Form.Item
          label={'数据库名称'}
          name={[SourceConfigEnum.SourceConfig, 'sourceSchema']}
          rules={[{required: true, message: '请选择数据库'}]}
        >
          <Select
            loading={databasesLoading}
            placeholder="请选择数据库"
            disabled={!compute}
            onDropdownVisibleChange={(open) => {
              if (open) {
                databasesRun();
              }
            }}
            onChange={(value) => {
              const curSourceType = form.getFieldValue([SourceConfigEnum.SourceConfig, 'sourceType']);
              const sourceSchema = [SourceTypeEnum.SQLServer, SourceTypeEnum.PostgreSQL].includes(
                curSourceType
              )
                ? value
                : undefined;
              form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceSchema'], value);
              form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceTable'], null);
            }}
          >
            {databasesList?.result?.databases.map((item) => (
              <Option key={item} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        </Form.Item>
      )}

      {sourceConnectionId && !['SQLServer', 'PostgreSQL', 'HANA'].includes(sourceType) ? (
        <Form.Item
          label={'数据库名称'}
          name={[SourceConfigEnum.SourceConfig, 'sourceDatabase']}
          rules={[{required: true, message: '请选择数据库'}]}
        >
          <Select
            loading={databasesLoading}
            placeholder="请选择数据库"
            disabled={!compute}
            onDropdownVisibleChange={(open) => {
              if (open) {
                databasesRun();
              }
            }}
            onChange={(value) => {
              form.setFieldValue([SourceConfigEnum.SourceConfig, 'sourceTable'], null);
            }}
          >
            {databasesList?.result?.databases.map((item) => (
              <Option key={item} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        </Form.Item>
      ) : (
        <Form.Item noStyle label="" name={[SourceConfigEnum.SourceConfig, 'sourceDatabase']} />
      )}

      {/* 必须有数据库 */}
      {shouldShowTableSelector && (
        <Form.Item
          label={'数据表名称'}
          name={[SourceConfigEnum.SourceConfig, 'sourceTable']}
          rules={[{required: true, message: '请选择数据表'}]}
        >
          <Select
            loading={tablesLoading}
            placeholder="请选择数据表"
            onSelect={(v: string) => {
              onChangeSourceTable?.(v);
            }}
            onDropdownVisibleChange={(open) => {
              if (open) {
                tablesRun();
              }
            }}
          >
            {tablesList?.result?.tables.map((item) => (
              <Option key={item} value={item}>
                {item}
              </Option>
            ))}
          </Select>
        </Form.Item>
      )}
    </>
  );
};

export default TableSelectFormItem;

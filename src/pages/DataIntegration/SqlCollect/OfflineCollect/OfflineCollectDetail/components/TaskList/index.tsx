import React, {useCallback, useState, useEffect, useImperative<PERSON>andle, forwardRef, useContext} from 'react';
import cx from 'classnames';
import {Link, Table, Pagination, Button, Space} from 'acud';
import {ColumnsType} from 'acud/lib/table';
import {useNavigate} from 'react-router-dom';
import useUrlState from '@ahooksjs/use-url-state';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {listExecutions} from '@/api/integration';
import {
  ListExecutionsReq,
  ExecutionInfo,
  ExecutionOrderBy,
  InstanceStatus,
  JobTriggerType,
  JobTriggerTypeMap,
  JobDetail,
  JobConfigStatus
} from '@/api/integration/type';
import urls from '@utils/urls';
import {formatTime} from '@utils/moment';
import {Order} from '@api/common';
import {InstanceStatusConfig} from '@/pages/DataIntegration/FileCollect/constants';
import {onStop} from '@/pages/DataIntegration/FileCollect/utils';
import {WorkspaceContext} from '@pages/index';
import IconSvg from '@components/IconSvg';

import styles from './index.module.less';
import {formatSeconds} from '@utils/utils';

interface TaskListProps {
  jobDetail: JobDetail;
  isPublished: boolean;
}

/**
 * 任务列表
 * @param jobDetail 任务详情
 */
const TaskList = forwardRef((props: TaskListProps, ref) => {
  const navigate = useNavigate();
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  const [{jobId}] = useUrlState();
  const {jobDetail, isPublished} = props;
  const [loading, setLoading] = useState(true);
  const [executionList, setExecutionList] = useState<ExecutionInfo[]>([]);
  const [status, setStatus] = useState<InstanceStatus>();
  const [triggerType, setTriggerType] = useState<JobTriggerType>();
  const [sorter, setSorter] = useState<{field: ExecutionOrderBy; order: Order}>();

  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 20,
    total: 0
  });

  useImperativeHandle(ref, () => ({
    refresh: getExecutionList
  }));

  // 查询运行记录列表
  const getExecutionList = useCallback(
    (params?: ListExecutionsReq) => {
      if (params?.pageNo) {
        setPagination((prev) => ({...prev, pageNo: params.pageNo!}));
      }
      setLoading(true);
      listExecutions(workspaceId, jobId, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        status,
        triggerType,
        orderBy: sorter?.field,
        order: sorter?.order,
        isPublished,
        ...(params || {})
      })
        .then((res) => {
          if (res.success) {
            setExecutionList(res?.result?.executions || []);
            setPagination((prev) => ({...prev, total: res?.result?.totalCount || 0}));
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [
      workspaceId,
      jobId,
      pagination.pageNo,
      pagination.pageSize,
      status,
      triggerType,
      sorter?.field,
      sorter?.order,
      isPublished
    ]
  );

  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSorter({field: sorter?.field, order: sorter?.order});
    setStatus(filters.status?.[0] as InstanceStatus);
    setTriggerType(filters.triggerType?.[0] as JobTriggerType);
  }, []);

  // 数据总数
  const showTotal = useCallback(() => {
    return `共${pagination.total}条`;
  }, [pagination.total]);

  // 跳转详情页
  const jumpDetail = useCallback(
    (runId: any) => {
      const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(jobDetail?.status);
      navigate(
        `${urls.offlineCollectResult}?jobId=${jobId}&runId=${runId}&taskName=${jobDetail.name}&isPublished=${isPublished}`
      );
    },
    [jobId, jobDetail]
  );

  useEffect(() => {
    getExecutionList();
  }, [getExecutionList]);

  const onExecutionStop = useCallback(
    (runId) => () => {
      onStop(jobId, workspaceId, runId, getExecutionList);
    },
    [getExecutionList, jobId, workspaceId]
  );

  // 渲染操作项
  const renderOperation = (record: ExecutionInfo) => {
    return (
      <Space size={12}>
        <Link
          onClick={() => {
            jumpDetail(record.runId);
          }}
        >
          详情
        </Link>
        {record.status === InstanceStatus.Running ? (
          <Link onClick={onExecutionStop(record.runId)}>终止</Link>
        ) : null}
      </Space>
    );
  };

  const columns: ColumnsType<ExecutionInfo> = [
    {
      title: '运行记录 ID',
      dataIndex: 'runId',
      key: 'runId',
      width: 180,
      ellipsis: {showTitle: false},
      render: (runId) => {
        return (
          <a onClick={() => jumpDetail(runId)}>
            <Ellipsis tooltip={runId}>{runId || '-'}</Ellipsis>
          </a>
        );
      }
    },
    {
      title: '业务时间',
      dataIndex: 'scheduleTime',
      key: 'scheduleTime',
      width: 180,
      render: (scheduleTime) => formatTime(scheduleTime)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      filters: Object.keys(InstanceStatusConfig).map((key) => ({
        text: InstanceStatusConfig[key].label,
        value: key
      })),
      filterMultiple: false,
      render: (key) => {
        const info = InstanceStatusConfig[key];
        return (
          <div className={styles['status-tag']} style={{color: info.textColor, background: info.color}}>
            {info.label}
          </div>
        );
      }
    },
    {
      title: '运行时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 120,
      render: (duration) => formatSeconds(duration)
    },
    // {title: '读取行数', dataIndex: 'readCount', key: 'readCount', width: 120},
    // {title: '写入行数', dataIndex: 'writeCount', key: 'writeCount', width: 120},
    // {title: '脏数据行数', dataIndex: 'dirtyCount', key: 'dirtyCount', width: 120},
    // {title: '读取大小', dataIndex: 'readBytes', key: 'readBytes', width: 120},
    // {title: '写入大小', dataIndex: 'writeBytes', key: 'writeBytes', width: 120},
    // {title: '脏数据大小', dataIndex: 'dirtyBytes', key: 'dirtyBytes', width: 120},
    {
      title: '运行类型',
      dataIndex: 'triggerType',
      key: 'triggerType',
      width: 120,
      filters: Object.keys(JobTriggerTypeMap).map((key) => ({
        text: JobTriggerTypeMap[key],
        value: key
      })),
      filterMultiple: false,
      render: (jobTriggerType) => {
        return JobTriggerTypeMap[jobTriggerType] || '-';
      }
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 180,
      render: (startTime) => formatTime(startTime)
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 180,
      render: (endTime) => formatTime(endTime)
    },
    {
      title: '操作',
      width: 130,
      fixed: 'right',
      render: (record: ExecutionInfo) => renderOperation(record)
    }
  ];

  return (
    <div className={styles['task-list-wrapper']}>
      <Button
        onClick={() => getExecutionList()}
        className={cx('mr-[10px]', 'mb-[12px]', 'square-button', styles['refresh-button'])}
      >
        <IconSvg type="refresh" size={16} color="#6c6d70" />
      </Button>
      <Table
        columns={columns}
        dataSource={executionList}
        rowKey="runId"
        onChange={handleTableChange}
        pagination={false}
        scroll={{y: 'calc(100vh - 400px)', ...(executionList?.length ? {} : {x: '100%'})}}
        loading={loading}
      />
      <div className={styles['pagination-container']}>
        <Pagination
          showSizeChanger={true}
          showQuickJumper={true}
          showTotal={showTotal}
          current={pagination.pageNo}
          total={pagination.total}
          defaultPageSize={20}
          onChange={(page, pageSize = 20) => {
            setPagination((prev) => ({
              ...prev,
              pageNo: page,
              pageSize: pageSize
            }));
            getExecutionList({
              pageNo: page,
              pageSize: pageSize
            });
          }}
        />
      </div>
    </div>
  );
});

TaskList.displayName = 'TaskList';

export default TaskList;

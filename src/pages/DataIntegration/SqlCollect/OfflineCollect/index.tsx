import {<PERSON>, <PERSON>Order<PERSON>y, <PERSON>T<PERSON>, RunStats, JobConfigStatus} from '@api/integration/type';
import urls from '@utils/urls';
import {Badge, Button, Dropdown, Link, Menu, Popover, Space, Table, Tag} from 'acud';
import React, {useCallback, useContext, useMemo, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';

import {Order} from '@api/common';
import {getIntegrationJobList} from '@api/integration';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {JobStatusConfig, orderMap} from '@pages/DataIntegration/FileCollect/constants';
import {getMetaUrl} from '@pages/DataIntegration/FileCollect/utils';
import {WorkspaceContext} from '@pages/index';
import {ColumnsType} from 'acud/lib/table';
import {useMemoizedFn, useRequest} from 'ahooks';
import classNames from 'classnames/bind';
import moment from 'moment';
import {JobStatusConfigMap, OperationShowType, SourceTypeEnum, SourceTypeMap} from '../constants';
import SearchFormItem from './components/SearchFormItem';
import styles from './index.module.less';
import {getOperationList, onCreate} from './utils';
import {getMetaUrlTable} from '../utils';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege, ResourceType} from '@api/permission/type';
import AuthButton from '@components/AuthComponents/AuthButton';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import {TooltipType} from '@components/AuthComponents/constants';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import PermissionModal, {PermissionModalRef} from '@components/Workspace/PermissionModal';
import PreCheckModal from './components/PreCheckModal';
import {Eye} from '@baidu/xicon-react-bigdata';
const cx = classNames.bind(styles);
export const PRIVILEGE_LIST = [
  {label: '查看', value: 'VIEW'},
  {label: '运行', value: 'EXECUTE'},
  {label: '管理', value: 'MANAGE'}
];

const filtersMap = {
  status: 'jobStatusFilter',
  sourceType: 'sourceType'
};
/**
 * 库表采集-列表
 */
const OfflineCollect: React.FC = () => {
  const permissionModalRef = useRef<PermissionModalRef>(null);
  // 权限列表
  const authList = useWorkspaceAuth([Privilege.StructuredIntegrationCreate, Privilege.CatalogMenu]);
  const navigate = useNavigate();
  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  const [searchObj, setSearchObj] = useState({
    keyword: 'namePattern',
    value: ''
  });
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState<{
    pageNo: number;
    pageSize?: number;
  }>({
    pageNo: 1,
    pageSize: 10
  });
  const [sorter, setSorter] = useState<{field: JobOrderBy; order: Order}>({
    field: JobOrderBy.CreateTime,
    order: Order.Desc
  });
  const [selectedRows, setSelectedRows] = useState<Job[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null); // 用于存储当前选中的任务

  const {
    data: dataSource,
    loading,
    run: getJobList
  } = useRequest(
    () => {
      const obj = {};
      Object.keys(filters).forEach((key) => {
        if (filters[key] && filtersMap[key]) {
          obj[filtersMap[key]] = filters[key].join(',');
        }
      });

      return getIntegrationJobList(workspaceId, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        orderBy: sorter.field,
        order: orderMap[sorter.order],
        type: JobType.Batch,
        [searchObj.keyword]: searchObj.value,
        ...obj
      });
    },
    {
      refreshDeps: [pagination, sorter, filters, workspaceId] //  分页 和 排序变化时刷新
    }
  );

  // 渲染最近运行状态
  const renderLatestRunStats = (latestRunStats: RunStats[]) => {
    if (!latestRunStats?.length) {
      return '-';
    }
    return (
      <div className={cx('status-wrapper')}>
        {latestRunStats?.map((item, index) => {
          const {runId, startTime, status} = item;
          const config = JobStatusConfig[status];
          const contentFields = [
            {label: '运行ID', value: runId},
            {label: '运行时间', value: moment(startTime).format('YYYY-MM-DD HH:mm:ss')},
            {label: '运行状态', value: config?.label}
          ];
          const content = contentFields.map((field, index) => (
            <div key={index}>
              <span>{field.label}：</span>
              <span>{field.value}</span>
            </div>
          ));
          return (
            <Popover key={index} placement="bottom" content={content}>
              <div className={cx('status', `status-${config?.icon}`)}></div>
            </Popover>
          );
        })}
      </div>
    );
  };

  // 渲染操作项
  const renderOperation = (record: Job, isMain: boolean) => {
    const operations = getOperationList(navigate, {
      status: record.status,
      privileges: record.privileges,
      isMain
    });
    const menu = (
      <Menu>
        {operations[OperationShowType.Dropdown].map((item, index) => (
          <AuthComponents key={index} isAuth={item.isAuth}>
            <Menu.Item
              key={index}
              onClick={() => item.callback([record], workspaceId, getJobList)}
              disabled={item.disabled}
            >
              {item.label}
            </Menu.Item>
          </AuthComponents>
        ))}
        <AuthMenuItem
          isAuth={record.privileges.includes(Privilege.Manage)}
          onClick={() =>
            permissionModalRef.current?.open({
              resourceType: ResourceType.StructuredIntegration,
              resourceId: record.jobId,
              resourceName: record.name,
              privilegeList: PRIVILEGE_LIST
            })
          }
        >
          权限管理
        </AuthMenuItem>
      </Menu>
    );
    return (
      <Space className={cx('operation')}>
        {operations[OperationShowType.List].map((item, index) => (
          <AuthComponents key={index} isAuth={item.isAuth}>
            <Button
              type="actiontext"
              key={index}
              onClick={() => item.callback([record], workspaceId, getJobList)}
              disabled={item.disabled}
            >
              {item.label}
            </Button>
          </AuthComponents>
        ))}
        <Dropdown overlay={menu}>
          <IconSvg type="more" size={16} color="#6c6d70" />
        </Dropdown>
      </Space>
    );
  };

  const columns: ColumnsType<Job> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      width: 200,
      ellipsis: {
        showTitle: false
      },
      render: (name, record) => {
        const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(record.status);
        return (
          <a
            onClick={() =>
              navigate(`${urls.offlineCollectDetail}?jobId=${record.jobId}&isPublished=${isPublished}`)
            }
          >
            <Ellipsis tooltip={name}>{name || '-'}</Ellipsis>
          </a>
        );
      },
      fixed: 'left'
    },

    {
      title: '状态',
      dataIndex: 'status',
      width: 150,
      filters: Object.keys(JobStatusConfigMap).map((item) => ({
        text: JobStatusConfigMap[item].label,
        value: item
      })),
      filterMultiple: false,
      render: (status, record) => {
        const validStatuses = [
          JobConfigStatus.PreCheck,
          JobConfigStatus.CheckPass,
          JobConfigStatus.CheckFail
        ];
        return (
          <Tag
            color="transparent"
            icon={<span className={`circle ${JobStatusConfigMap[status]?.icon}`}></span>}
          >
            {JobStatusConfigMap[status]?.label}
            {validStatuses.includes(status) && (
              <Button
                icon={<Eye theme="line" color="#000000" size={16} strokeLinejoin="round" />}
                onClick={() => {
                  setSelectedJob(record);
                  setModalVisible(true);
                }}
                type="actiontext"
              />
            )}
          </Tag>
        );
      }
    },
    {
      title: '源端类型',
      dataIndex: 'sourceType',
      filters: Object.keys(SourceTypeEnum).map((item) => ({
        text: item,
        value: item
      })),
      width: 110,
      filterMultiple: false,
      render: (text) => (
        <div className={cx('source-type')}>
          <IconSvg type={SourceTypeMap[text]?.icon} className="bordered-circle-icon mr-[8px]" size={14} />
          {text}
        </div>
      )
    },
    {
      title: '源端数据源',
      dataIndex: 'sourceConnectionId',
      width: 120,
      // 点击新开页签至数据源页面，根据数据源名称进行检索
      render: (text, record) => (
        <a
          onClick={() =>
            navigate(`${urls.connectionDetail}?name=${record.sourceConnectionId}&workspaceId=${workspaceId}`)
          }
        >
          <Ellipsis tooltip={text}>{text || '-'}</Ellipsis>
        </a>
      )
    },
    {
      title: '源端数据库',
      dataIndex: 'sourceDatabase',
      width: 120
    },
    {
      title: '源端数据表',
      dataIndex: 'sourceTable',
      width: 120
    },
    {
      title: '目标端数据表',
      dataIndex: 'sinkTable',
      width: 200,
      // 点击新开页签至当前路径对应的元数据列表
      render: (text, record) =>
        text && text !== '暂未创建' ? (
          <a onClick={() => navigate(getMetaUrlTable(text, workspaceId))}>
            <Ellipsis tooltip={text}>{text?.split('.')?.[2] || '-'}</Ellipsis>
          </a>
        ) : (
          text
        )
    },
    {
      title: '最近运行',
      dataIndex: 'latestRunStats',
      render: (text) => renderLatestRunStats(text),
      width: 160
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 100,
      render: (text) => (
        <TextEllipsis tooltip={text} width={100}>
          {text}
        </TextEllipsis>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      sorter: true,
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
      width: 160
    },
    {
      title: '操作',
      fixed: 'right',
      width: 260,
      dataIndex: 'operation',
      render: (_, record) => renderOperation(record, true)
    }
  ];

  // 子表格的列定义
  const subColumns: ColumnsType<Job> = columns.map((col) => {
    const {filters, sorter, filterMultiple, ...rest} = col;
    if ('dataIndex' in col && col.dataIndex === 'operation') {
      return {
        ...rest,
        render: (_, record) => renderOperation(record, false)
      };
    }

    return {...rest};
  });

  // 分页变化
  const onPageChange = (page, pageSize) => {
    setPagination({
      pageNo: page,
      pageSize: pageSize
    });
  };
  // 表格变化
  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSorter({field: sorter.field, order: sorter.order});
    setFilters(filters);
  }, []);

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys: selectedRows.map((item: Job) => item.jobId),
      onChange: (_, selectedRows: Job[]) => {
        setSelectedRows(selectedRows);
      }
    }),
    [selectedRows]
  );

  const searchFn = useMemoizedFn(() => {
    setPagination((pre) => ({...pre, pageNo: 1}));
  });

  return (
    <div>
      <div className={cx('header')}>
        <SearchFormItem setSearchObj={setSearchObj} searchObj={searchObj} searchFn={searchFn} />
        <div className={cx('header-right')}>
          <Button onClick={getJobList} className="mr-[10px]">
            <IconSvg type="refresh" size={16} />
          </Button>
          {/* <Button className="mr-[10px]">运行</Button>
          <Button className="mr-[10px]">发布</Button>
          <Button className="mr-[10px]">删除</Button>
          <Button className="mr-[10px]">编辑</Button> */}
          <AuthButton
            isAuth={authList[Privilege.StructuredIntegrationCreate]}
            tooltipType={TooltipType.Function}
            type="primary"
            onClick={() => onCreate(navigate)}
            className="mr-[10px]"
            icon={<IconSvg type="add" size={16} />}
          >
            创建
          </AuthButton>
          <div>已经选择{rowSelection?.selectedRowKeys?.length ?? 0}项</div>
        </div>
      </div>

      <Table
        scroll={{x: 1200}}
        columns={columns}
        dataSource={dataSource?.result?.jobs as Job[]}
        loading={loading}
        pagination={{
          current: pagination.pageNo,
          pageSize: pagination.pageSize,
          total: dataSource?.result?.totalCount || 0,
          pageSizeOptions: ['10', '20', '50', '100'],
          showSizeChanger: true,
          onChange: onPageChange
        }}
        onChange={handleTableChange}
        rowKey="jobId"
        // rowSelection={{
        //   type: 'checkbox',
        //   ...rowSelection
        // }}
        expandable={{
          expandedRowRender: (record) => {
            const {draft, privileges} = record;
            if (!record?.draft) {
              return null;
            }

            const dataSource = [{...draft, privileges}];

            return (
              <Table
                scroll={{x: 1200}}
                columns={subColumns}
                dataSource={dataSource}
                loading={loading}
                pagination={false}
              />
            );
          },
          rowExpandable: (record) => record.status === JobConfigStatus.Updating
        }}
      />
      <PermissionModal workspaceId={workspaceId} ref={permissionModalRef} />
      {modalVisible && (
        <PreCheckModal
          jobs={[selectedJob]}
          workspaceId={workspaceId}
          afterClose={() => setModalVisible(false)}
          isPreCheck={false}
        />
      )}
    </div>
  );
};

export default OfflineCollect;

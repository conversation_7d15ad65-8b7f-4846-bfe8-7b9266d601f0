import urls from '@utils/urls';
import {<PERSON><PERSON><PERSON>rum<PERSON>, Divider, Link} from 'acud';
import React, {useEffect, useState, useRef} from 'react';
import cx from 'classnames';
import {useNavigate} from 'react-router-dom';
import styles from './index.module.less';
import IconSvg from '@components/IconSvg';

import Header from './component/header';
import InfoCard from './component/infoCard';
import TaskLog, {TaskLogRefHandle} from './component/taskLog';
import InstanceLog, {InstanceLogRefHandle} from './component/InstanceLog';

import {getIntegrationExecutionLog} from '@api/integration';
import {IntegrationTab} from '@pages/DataIntegration/constants';

import useUrlState from '@ahooksjs/use-url-state';

const kclass = 'sql-collect-result-main';
/**
 * 离线同步 - 运行记录详情
 */
const OfflineCollectResult: React.FC = () => {
  const navigate = useNavigate();

  const [urlState] = useUrlState({jobId: '', runId: '', taskName: '', isPublished: true});

  const taskLogHandleRef = useRef<TaskLogRefHandle>(null);
  const instanceLogHandleRef = useRef<InstanceLogRefHandle>(null);

  // 卡片信息：mock 数据
  const statList = [
    {
      icon: <IconSvg type="meta-volume" size={20} />,
      label: '已读取数据量',
      rowSize: '19W',
      rowUnit: '行',
      infoSize: '4.77',
      infoUnit: 'GB'
    },
    {
      icon: <IconSvg type="meta-volume" size={20} />,
      label: '已写入数据量',
      rowSize: '19.1K',
      rowUnit: '行',
      infoSize: '4.77',
      infoUnit: 'GB'
    },
    {
      icon: <IconSvg type="meta-volume" size={20} />,
      label: '脏数据量',
      rowSize: '13.2W',
      rowUnit: '行',
      infoSize: '4.77',
      infoUnit: 'GB'
    },
    {
      icon: <IconSvg type="meta-volume" size={20} />,
      label: '读取速率',
      rowSize: '1.1W',
      rowUnit: '行',
      infoSize: '4.77',
      infoUnit: 'GB'
    },
    {
      icon: <IconSvg type="meta-volume" size={20} />,
      label: '已读取数据量',
      rowSize: '1.9K',
      rowUnit: '行',
      infoSize: '4.77',
      infoUnit: 'GB'
    }
  ];

  // 自动刷新
  const [taskFinished, setTaskFinished] = useState(false);

  // 跳转至离线采集页
  const jumpOffline = () => {
    navigate(`${urls.integration}?tab=${IntegrationTab.Offline}`);
  };

  const jumpTaskDetail = () => {
    navigate(`${urls.offlineCollectDetail}?jobId=${urlState.jobId}&isPublished=${urlState.isPublished}`);
  };

  const refreshLogWhenAuto = () => {
    instanceLogHandleRef.current?.refresh();
  };

  const refreshLogWhenManual = () => {
    instanceLogHandleRef.current?.manualRefresh();
  };

  useEffect(() => {}, []);

  return (
    <div className={cx('db-workspace-wrapper', styles[`${kclass}`])}>
      {/* 面包屑 */}
      <Breadcrumb>
        <Breadcrumb.Item>
          <Link onClick={jumpOffline}>库表采集</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link onClick={jumpOffline}>离线同步</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link onClick={jumpTaskDetail}>{urlState.taskName || '-'}</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>运行记录详情</Breadcrumb.Item>
      </Breadcrumb>
      {/* 头部信息 */}
      <div className={styles[`${kclass}-header`]}>
        <Header taskName={urlState.runId}></Header>
      </div>
      {/* 分割线 */}
      <Divider className={styles[`${kclass}-diver`]}></Divider>
      {/* 统计信息卡片 */}
      {/* 本期不支持卡片，但需要用到刷新和自动刷新间隔 */}
      <InfoCard
        cardList={statList}
        refreshLogWhenAuto={refreshLogWhenAuto}
        refreshLogWhenManual={refreshLogWhenManual}
      ></InfoCard>
      {/* 日志区块 */}
      {/* <TaskLog
        ref={taskLogHandleRef}
        queryLogMethod={getIntegrationExecutionLog}
        urlState={urlState}
        autoRefresh={taskFinished}
        updateAutoRefresh={(value: boolean) => {
          setTaskFinished(value);
        }}
      ></TaskLog> */}
      <InstanceLog ref={instanceLogHandleRef} runId={urlState.runId} />
    </div>
  );
};

export default OfflineCollectResult;

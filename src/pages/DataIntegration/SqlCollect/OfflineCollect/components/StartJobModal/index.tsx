import {startIntegrationJob, batchOperationIntegrationJob} from '@api/integration';
import {IJobItem, JobTriggerType, JobType, BatchOperateType, ExecutionInfo} from '@api/integration/type';
import {batchListLatestExecutions} from '@api/integration';
import urls from '@utils/urls';
import {Alert, Button, Collapse, DatePicker, Link, Modal, Space, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import moment, {Moment} from 'moment';
import React, {useState, useEffect} from 'react';
const {Panel} = Collapse;

// 运行任务弹窗
// 该弹窗在 730 版本中被非结构化文件集成复用，待后续作为公共组件提出
// 非结构化集成相比结构化离线集成
// - 暂不支持查看实例详情
// - 新增支持非批量运行的运行风险提示（如果有正在运行的实例，则提示）
// - 支持批量任务运行
// 以上特性已合入，但暂不对结构化启用
const StartJobModal: React.FC<{
  jobType: JobType;
  jobs: IJobItem[];
  workspaceId: string;
  callback: () => void;
  navigate: (path: string) => void;
  afterClose: () => void;
}> = ({jobType = JobType.Batch, jobs, workspaceId, callback, navigate, afterClose}) => {
  const [visible, setVisible] = useState(true);
  const [scheduleTime, setScheduleTime] = useState<Moment>(moment());
  // 如果 job 没有 name 字段，则使用 '任务' 作为默认名称（适用于在 Create/Edit 页面创建任务后直接运行，没有 name 传值）
  const jobNames = jobs.map((job) => `"${job.name || '任务'}"`).join('、');
  /** 是否批量任务 */
  const isBatch = jobs.length > 1;
  /** 运行实例列表，用于 Alert 提示风险 */
  const [runningInstanceList, setRunningInstanceList] = useState<ExecutionInfo[]>([]);

  // 运行任务 是否查看详情
  const runJob = useMemoizedFn(async (flag: boolean) => {
    const isPublished = ['PUBLISHED', 'UPDATING'].includes(jobs[0].status);
    const {result, success} = isBatch
      ? await batchOperationIntegrationJob(workspaceId, BatchOperateType.Start, {
          jobIds: jobs.map((item) => item.jobId),
          runtimeArgs: {
            scheduleTime: scheduleTime.format('YYYY-MM-DD HH:mm:ss'),
            triggerType: JobTriggerType.Once
          }
        })
      : await startIntegrationJob(workspaceId, jobs[0].jobId, {
          isPublished,
          runtimeArgs: {
            triggerType: JobTriggerType.Once,
            scheduleTime: scheduleTime.format('YYYY-MM-DD HH:mm:ss')
          }
        });

    if (!success) {
      return;
    }
    setVisible(false);

    const runId = result?.['runId'];
    const jobId = result?.['jobId'];

    if (flag) {
      navigate(
        `${urls.offlineCollectResult}?jobId=${jobId}&runId=${runId}&taskName=${jobs[0]?.name}&isPublished=${isPublished}`
      );

      toast.success({
        message: `${jobNames}运行成功！`,
        duration: 5
      });
    } else {
      // 成功 Toast，当前只有结构化离线集成支持查看详情
      toast.success({
        message: `${jobNames}运行提交成功`,
        description:
          jobType === JobType.Batch && !isBatch ? (
            <span>
              请前往运行记录查看结果，立即前往
              <Link
                className="global-notify-ticket-link cursor-pointer"
                onClick={() => {
                  const isPublished = ['PUBLISHED', 'UPDATING'].includes(jobs[0].status);
                  navigate(
                    `${urls.offlineCollectResult}?jobId=${jobId}&runId=${runId}&taskName=${jobs[0]?.name}&isPublished=${isPublished}`
                  );
                }}
              >
                运行记录
              </Link>
            </span>
          ) : null,
        duration: 5
      });
      callback();
    }
  });

  // 加载运行实例列表数据
  const loadRunningInstanceList = useMemoizedFn(async () => {
    const {result, success} = await batchListLatestExecutions(
      workspaceId,
      jobs.map((item) => item.jobId),
      {
        status: 'RUNNING,READY'
      }
    );
    if (!success) {
      return;
    }
    setRunningInstanceList(result.executions);
  });

  // 当窗口挂载，且类型为文件离线集成时，加载运行实例列表数据
  useEffect(() => {
    if (jobType === JobType.File) {
      loadRunningInstanceList();
    }
  }, [jobType, loadRunningInstanceList]);

  const footer = (
    <Space>
      <Button type="default" onClick={() => setVisible(false)}>
        取消
      </Button>
      {jobType === JobType.File && (
        <Button type="primary" onClick={() => runJob(false)}>
          运行
        </Button>
      )}
      {jobType !== JobType.File && (
        <>
          <Button type="enhance" onClick={() => runJob(false)}>
            运行
          </Button>
          {/* 批量无法查看详情 */}
          {!isBatch && (
            <Button type="primary" onClick={() => runJob(true)}>
              运行并查看详情
            </Button>
          )}
        </>
      )}
    </Space>
  );
  return (
    <Modal
      visible={visible}
      title={`${isBatch ? '批量' : ''}运行`}
      width={632}
      footer={footer}
      onCancel={() => setVisible(false)}
      afterClose={afterClose}
    >
      {/* 风险提示暂时只支持文件离线集成运行 */}
      {jobType === JobType.File && jobs.length > 0 && runningInstanceList.length > 0 ? (
        <Alert
          type="warning"
          showIcon
          message={
            <>
              任务存在待触发、运行中实例，可能导致重复写入或资源冲突等问题，请确认后再执行
              <br />
              您可在「任务列表-最近运行」查看正在执行的任务，通过任务名称查看该任务的详细运行记录
            </>
          }
        />
      ) : (
        <Alert
          message="您可在「任务列表-最近运行」查看正在执行的任务，通过任务名称查看该任务的详细运行记录"
          type="info"
        />
      )}
      <div className="flex items-center pt-4">
        <span className="mr-2">业务时间</span>
        <DatePicker value={scheduleTime} onChange={setScheduleTime} showTime format="YYYY-MM-DD HH:mm:ss" />
      </div>
    </Modal>
  );
};

export default StartJobModal;

import {
  getValidateResult,
  integrationForcePassValidation,
  integrationValidateJob,
  publishIntegrationJob
} from '@api/integration';
import {IJobItem, JobConfigStatus, PreCheckResultItem, JobType} from '@api/integration/type';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {IntegrationTab} from '@pages/DataIntegration/constants';
import {EditMode} from '@pages/DataIntegration/FileCollect/constants';
import {onStart} from '@pages/DataIntegration/FileCollect/FileColletRunModal';
import urls from '@utils/urls';
import {Al<PERSON>, Button, Collapse, Modal, Space, Tag} from 'acud';
import {useMemoizedFn, useRequest} from 'ahooks';
import React, {useEffect, useMemo, useState} from 'react';
import OnOpenModal, {ModalTypeEnum} from '../OnOpenModal';
const {Panel} = Collapse;

// 前置检查弹窗
const PreCheckModal: React.FC<{
  jobs: IJobItem[];
  workspaceId: string;
  callback?: () => void;
  navigate?: (path: string) => void;
  afterClose?: () => void;
  isPreCheck: boolean;
  type?: IntegrationTab; // 离线集成 文件集成
}> = ({jobs, workspaceId, callback, navigate, afterClose, isPreCheck, type = IntegrationTab.Offline}) => {
  // 自动刷新
  const [jobFinished, setJobFinished] = useState(
    isPreCheck ? false : jobs[0].status !== JobConfigStatus.PreCheck
  );
  const [success, setSuccess] = useState(isPreCheck ? false : jobs[0].status !== JobConfigStatus.CheckPass);
  const [canForcePass, setCanForcePass] = useState(false);
  const [resultItems, setResultItems] = useState<PreCheckResultItem[]>([null]);
  const [defaultActiveKey, setDefaultActiveKey] = useState<string[]>([]);
  const [visible, setVisible] = useState(true);
  const [canRunCallback, setCanRunCallback] = useState<boolean>(true);

  const initFn = async () => {
    const isPublished = ['PUBLISHED', 'UPDATING'].includes(jobs[0].status);
    isPreCheck && (await integrationValidateJob(workspaceId, jobs[0].jobId, {isPublished}));
    getValidateResultFn(workspaceId, jobs[0].jobId, {isPublished});
  };

  // 获取前置检查结果
  const {run: getValidateResultFn} = useRequest(getValidateResult, {
    manual: true,
    defaultParams: [
      workspaceId,
      jobs[0].jobId,
      {isPublished: ['PUBLISHED', 'UPDATING'].includes(jobs[0].status)}
    ],
    pollingInterval: !isPreCheck || jobFinished ? undefined : 5000, // 每 5s 轮询，taskFinished 为 true / 眼睛展示 时停止
    pollingWhenHidden: false,
    onSuccess: ({result}) => {
      // 前置检查完成
      setJobFinished(result?.result?.length > 0 && result.result.every((item) => !!item?.status));
      setDefaultActiveKey(result?.result?.filter((item) => item.status === 'fail').map((item) => item.name));
      // 前置检查是否通过
      setSuccess(result.success);
      // 前置检查是否可以强制通过
      setCanForcePass(result.canForcePass);
      // 前置检查结果
      setResultItems(result.result);
    }
  });

  useEffect(() => {
    initFn();
  }, []);

  const runJob = useMemoizedFn(() => {
    switch (type) {
      case IntegrationTab.FileCollect:
        OnOpenModal(ModalTypeEnum.StartJob, jobs, workspaceId, callback, navigate, JobType.File);
        break;
      case IntegrationTab.Offline:
        OnOpenModal(ModalTypeEnum.StartJob, jobs, workspaceId, callback, navigate, JobType.Batch);
        break;
      default:
        OnOpenModal(ModalTypeEnum.StartJob, jobs, workspaceId, callback, navigate, JobType.Batch);
        break;
    }
    setVisible(false);
  });

  const publishJob = useMemoizedFn(() => {
    publishIntegrationJob(workspaceId, jobs[0].jobId);
    setVisible(false);
  });

  const forcePass = useMemoizedFn(() => {
    const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(
      jobs[0].status as JobConfigStatus
    );
    integrationForcePassValidation(workspaceId, jobs[0].jobId, {isPublished}).then(({result}) => {
      setVisible(false);
    });
  });

  const editJob = useMemoizedFn(() => {
    // 需要跳转到 Edit 页时，阻止弹窗关闭时触发回调，避免二次跳转
    setCanRunCallback(false);
    const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(
      jobs[0].status as JobConfigStatus
    );
    switch (type) {
      case IntegrationTab.FileCollect:
        navigate(
          `${urls.fileCollectCreate}?jobId=${jobs[0].jobId}&mode=${EditMode.Edit}&isPublished=${isPublished}`
        );
        break;
      case IntegrationTab.Offline:
        navigate(`${urls.offlineCollectConfig}?jobId=${jobs[0].jobId}&isPublished=${isPublished}`);

        break;
      default:
        console.log(jobs);
        if (jobs[0].type === JobType.File) {
          navigate(`${urls.fileCollectCreate}?jobId=${jobs[0].jobId}&isPublished=${isPublished}&mode=edit`);
        } else {
          navigate(`${urls.offlineCollectConfig}?jobId=${jobs[0].jobId}&isPublished=${isPublished}`);

          break;
        }
    }
    setVisible(false);
  });

  // 获取类型图标和标签
  const getTypeMap = (type: string) => {
    switch (type) {
      case 'pass':
        return {
          icon: 'success',
          label: '成功'
        };
      case 'fail':
        return {
          icon: 'error',
          label: '失败'
        };
      case 'warning':
        return {
          icon: 'warning',
          label: '警告'
        };

      default:
        return {
          icon: 'processing',
          label: '检查中'
        };
    }
  };

  useEffect(() => {
    // 关闭弹窗后，执行回调
    if (!visible && isPreCheck) {
      if (canRunCallback) {
        callback();
      }
    }
  }, [visible, callback, isPreCheck, canRunCallback]);

  function changeActive(key) {
    setDefaultActiveKey(key);
  }

  const footer = useMemo(() => {
    if (!isPreCheck) return null;
    return (
      <Space>
        {/* <Button type="default" onClick={() => setVisible(false)}>
          取消
        </Button> */}
        {success && jobFinished && (
          <Button type="default" onClick={publishJob}>
            发布
          </Button>
        )}

        {success && jobFinished && (
          <Button type="primary" onClick={runJob}>
            运行
          </Button>
        )}

        {!success && canForcePass && jobFinished && (
          <Button type="default" onClick={forcePass}>
            强制通过
          </Button>
        )}
        {!success && jobFinished && (
          <Button type="default" onClick={editJob}>
            编辑任务
          </Button>
        )}
      </Space>
    );
  }, [isPreCheck, success, jobFinished, runJob, canForcePass, forcePass, editJob]);
  return (
    <Modal
      visible={visible}
      title={`前置检查`}
      width={800}
      footer={footer}
      onCancel={() => setVisible(false)}
      afterClose={afterClose}
    >
      {!jobFinished && (
        <Alert
          className="mb-4"
          message={<div>前置检查中，请耐心等待，请勿离开页面</div>}
          type="info"
          showIcon
        />
      )}
      {success && jobFinished && (
        <Alert
          className="mb-4"
          message={<div>前置检查通过，您可以运行任务</div>}
          // message={<div>前置检查通过，您可以运行或发布任务</div>}
          type="success"
          showIcon
        />
      )}
      {!success && jobFinished && (
        <Alert
          className="mb-4"
          message={<div>前置检查未通过，失败原因详见以下检测结果</div>}
          type="error"
          showIcon
        />
      )}
      <Collapse activeKey={defaultActiveKey} onChange={changeActive}>
        {resultItems?.map((item) => (
          <Panel
            header={
              <div className="flex items-center justify-between">
                <Ellipsis tooltip={item?.subscription}>{item?.subscription}</Ellipsis>
                <Tag
                  color="transparent"
                  icon={<span className={`circle status-${getTypeMap(item?.status).icon}`} />}
                >
                  {getTypeMap(item?.status).label}
                </Tag>
              </div>
            }
            key={item?.name}
            showArrow={!!item?.subItems}
            disabled={!item?.subItems}
          >
            <p>{item?.message}</p>
            <Collapse>
              {item?.subItems?.map((subItem) => (
                <Panel
                  header={
                    <div className="flex items-center justify-between">
                      <Ellipsis tooltip={subItem?.subscription || '-'}>
                        {subItem?.subscription || '-'}
                      </Ellipsis>
                      <Tag
                        color="transparent"
                        icon={<span className={`circle status-${getTypeMap(subItem?.status).icon}`} />}
                      >
                        {getTypeMap(subItem?.status).label}
                      </Tag>
                    </div>
                  }
                  key={subItem?.name}
                  showArrow={!!subItem?.message}
                >
                  <p>{subItem?.message}</p>
                </Panel>
              ))}
            </Collapse>
          </Panel>
        ))}
      </Collapse>
    </Modal>
  );
};

export default PreCheckModal;

import {OperateArgs, JobType} from '@api/integration/type';
import {ConfigProvider} from 'acud';
import locale from 'acud/lib/locale/zh_CN';
import ReactDOM from 'react-dom/client';
import OfflineCollectCreate from '../OfflineCollectCreate';
import PreCheckModal from '../PreCheckModal';
import StartJobModal from '../StartJobModal';
import {IntegrationTab} from '@pages/DataIntegration/constants';

// 弹窗类型
export enum ModalTypeEnum {
  StartJob = 'startJob',
  PreCheck = 'preCheck',
  FilePreCheck = 'filePreCheck',
  Create = 'create'
}
/**
 * 命令式打开弹窗
 * @param params 参数  jobs: JobItem[], workspaceId: string, callback: () => void, navigate: (path: string) => void
 * 目前使用 会导致 添加组件脱离 父组件 的 context，重新配置 locale 等
 * 无法获取 父组件的 路由，传递 navigate
 * @returns
 */
export default function OnOpenModal(type: ModalTypeEnum, ...params: OperateArgs): void {
  // 730 版支持不同任务类型复用 Modal 组件，但任务类型 默认为 离线任务，保持对旧版的兼容
  const [jobs, workspaceId, callback, navigate, jobType = JobType.Batch] = params;
  const container = document.createElement('div');
  document.body.appendChild(container);

  const root = ReactDOM.createRoot(container);
  const afterClose = () => {
    root.unmount();
    container.remove();
  };

  const ModalComponent = {
    [ModalTypeEnum.StartJob]: (
      <StartJobModal
        jobType={jobType}
        jobs={jobs}
        workspaceId={workspaceId}
        callback={callback}
        navigate={navigate}
        afterClose={afterClose}
      />
    ),
    [ModalTypeEnum.PreCheck]: (
      <PreCheckModal
        jobs={jobs}
        workspaceId={workspaceId}
        callback={callback}
        navigate={navigate}
        afterClose={afterClose}
        isPreCheck={true}
      />
    ),
    [ModalTypeEnum.FilePreCheck]: (
      <PreCheckModal
        jobs={jobs}
        workspaceId={workspaceId}
        callback={callback}
        navigate={navigate}
        afterClose={afterClose}
        isPreCheck={true}
        type={IntegrationTab.FileCollect}
      />
    ),
    [ModalTypeEnum.Create]: (
      <OfflineCollectCreate callback={callback} navigate={navigate} afterClose={afterClose} />
    )
  };

  root.render(<ConfigProvider locale={locale}>{ModalComponent[type]}</ConfigProvider>);
}

import {IJobItem, JobConfigStatus} from '@api/integration/type';
import IconSvg from '@components/IconSvg';
import urls from '@utils/urls';
import {Button, Modal, Space} from 'acud';
import {useMemoizedFn} from 'ahooks';
import classNames from 'classnames/bind';
import React, {useMemo, useState} from 'react';
import {SourceTypeMap} from '../../../constants';
import styles from './index.module.less';
const cx = classNames.bind(styles);

// 前置检查弹窗
const PreCheckModal: React.FC<{
  jobs: IJobItem[];
  navigate: (path: string) => void;
  callback: (v?: any) => void;
  afterClose?: () => void;
}> = ({jobs, navigate, afterClose}) => {
  const [visible, setVisible] = useState(true);
  const [activeItem, setActiveItem] = useState<string | null>(null);

  const handleOk = useMemoizedFn(() => {
    const isPublished =
      Array.isArray(jobs) && jobs.length > 0
        ? [JobConfigStatus.Published, JobConfigStatus.Updating].includes(jobs[0]?.status as JobConfigStatus)
        : true;
    navigate(urls.offlineCollectConfig + `?sourceType=${activeItem}&isPublished=${isPublished}`);
    afterClose?.();
  });
  const footer = useMemo(
    () => (
      <Space>
        <Button type="default" onClick={() => setVisible(false)}>
          取消
        </Button>
        <Button disabled={!activeItem} type="primary" onClick={() => handleOk()}>
          确定
        </Button>
      </Space>
    ),
    [activeItem, handleOk]
  );
  return (
    <Modal
      visible={visible}
      title={`创建`}
      width={512}
      footer={footer}
      onCancel={() => setVisible(false)}
      afterClose={afterClose}
    >
      <div className={styles['title']}>结构化数据</div>
      <div className={styles['card-list']}>
        {Object.values(SourceTypeMap).map((item) => (
          <div
            onClick={() => setActiveItem(item.value)}
            key={item.value}
            className={cx('card-item', {active: activeItem === item.value})}
          >
            <div className={styles['card-item-icon']}>
              <IconSvg type={item.icon} size={32} />
            </div>
            <div className={styles['card-item-label']}>{item.label}</div>
          </div>
        ))}
      </div>
    </Modal>
  );
};

export default PreCheckModal;

/**
 * 工作流 常用静态方法
 * <AUTHOR>
 */
import {ICron, ICronForm, IJob} from '@api/job';
import {Privilege} from '@api/permission/type';
import {CronItemTypeEnum, CronTypeEnum, CronWeekArr, JSON_FORMAT} from '@pages/JobWorkflow/constants';
import {isArray} from 'lodash';
import moment from 'moment';

/** 处理 cron 表达式 转义
 * @param cron cron 表达式（后端格式） 主要使用 type, crontab
 * @returns 转义后的字符串 例如：每小时第0分钟运行
 *
 * @example
 * dealCronToStr({
 *   type: CronTypeEnum.HOUR,
 *   crontab: '0 0 * * * ?'
 * }) // 每小时第0分钟运行
 */
export const dealCronToStr = (cron?: ICron): string => {
  if (!cron || !cron.crontab) {
    return '-';
  }
  const {type, crontab} = cron;
  const [second, minute, hour, day, month, week] = crontab.split(' ');
  const dayStr = day
    ?.split(',')
    .sort((a, b) => Number(a) - Number(b))
    .join(',');
  const weekStr = week
    ?.split(',')
    .sort((a, b) => Number(a) - Number(b))
    .join(',');
  const hourStr = hour.length === 1 ? `0${hour}` : hour;
  const minuteStr = minute.length === 1 ? `0${minute}` : minute;
  switch (type) {
    case CronTypeEnum.HOUR:
      return `每小时第${minute}分钟运行`;
    case CronTypeEnum.DAY:
      return `每天${hourStr}:${minuteStr}运行`;
    case CronTypeEnum.WEEK:
      return `每周${weekStr
        .split(',')
        .map((item) => CronWeekArr[Number(item) - 1].label)
        .join('、')} ${hourStr}:${minuteStr}运行`;
    case CronTypeEnum.MONTH:
      return `每月${dayStr}日${hourStr}:${minuteStr}运行`;
    case CronTypeEnum.YEAR:
      return `每年${month}月${dayStr}日${hourStr}:${minuteStr}运行`;
    default:
      return crontab;
  }
};

/** 处理 cron 表达式 转义 到 form 表单
 * @param cron cron 表达式（后端格式）
 * @returns form 表单 使用 startTime, endTime, crontab, type, minute
 *
 * @example
 * dealCronToForm({
 *   type: CronTypeEnum.HOUR,
 *   crontab: '0 0 * * * ?'
 * }) // {startTime: moment().startOf('day'), endTime: moment().endOf('day').add(100, 'year'), crontab: '0 0 * * * ?', type: CronTypeEnum.HOUR, minute: 0}
 */
export const dealCronToForm = (cron: ICron | null): ICronForm => {
  if (!cron) {
    return {
      startTime: moment().startOf('day'),
      endTime: moment().endOf('day').add(100, 'year'),
      crontab: '0 0 * * * ?',
      type: CronTypeEnum.HOUR,
      minute: 0
    };
  }
  const {type, crontab, startTime, endTime} = cron;

  // 默认值
  const defaultValues: ICronForm = {
    startTime: moment(startTime),
    endTime: moment(endTime),
    crontab: crontab || '',
    type: type as CronTypeEnum
  };
  const [_, minute, hour, day, month, week] = defaultValues.crontab.split(' ');
  defaultValues[CronItemTypeEnum.MINUTE] = Number(minute);
  defaultValues[CronItemTypeEnum.DAY] = day
    ?.split(',')
    .map(Number)
    .filter((item) => !isNaN(item));
  defaultValues[CronItemTypeEnum.MONTH] = month
    ?.split(',')
    .map(Number)
    .filter((item) => !isNaN(item));
  defaultValues[CronItemTypeEnum.WEEK] = week
    ?.split(',')
    .map(Number)
    .filter((item) => !isNaN(item));
  defaultValues[CronItemTypeEnum.TIME] = moment().hour(Number(hour)).minute(Number(minute));
  return defaultValues;
};

/** 处理 form 表单 转义 到 cron 表达式
 * @param form  表单 使用 startTime, endTime, crontab, type, minute
 * 根据 type 不同 取不同的值 构建 crontab表达式
 * @returns cron 表达式（后端格式）
 *
 * @example
 * dealFormToCron({
 *   startTime: moment().startOf('day'), endTime: moment().endOf('day').add(100, 'year'), crontab: '0 0 * * * ?', type: CronTypeEnum.HOUR, minute: 0
 * }) // {type: CronTypeEnum.HOUR, crontab: '0 0 * * * ?'}
 */
export const dealFormToCron = (form?: ICronForm): ICron => {
  if (!form)
    return {
      startTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      endTime: moment().endOf('day').add(100, 'year').format('YYYY-MM-DD HH:mm:ss'),
      crontab: '0 0 * * * ?',
      type: CronTypeEnum.HOUR
    };
  const {minute, day, month, week, type, time} = form;
  const startHour = moment(time).hour();
  const startMinute = moment(time).minute();
  let crontab = '';
  switch (type) {
    case CronTypeEnum.HOUR:
      crontab = `0 ${minute} * * * ?`;
      break;
    case CronTypeEnum.DAY:
      crontab = `0 ${startMinute} ${startHour} * * ?`;
      break;
    case CronTypeEnum.WEEK:
      crontab = `0 ${startMinute} ${startHour} ? * ${week?.sort((a, b) => Number(a) - Number(b)).join(',')}`;
      break;
    case CronTypeEnum.MONTH:
      crontab = `0 ${startMinute} ${startHour} ${day?.sort((a, b) => Number(a) - Number(b)).join(',')} * ?`;
      break;
    case CronTypeEnum.YEAR:
      crontab = `0 ${startMinute} ${startHour} ${day?.sort((a, b) => Number(a) - Number(b)).join(',')} ${month?.sort((a, b) => Number(a) - Number(b)).join(',')} ?`;
      break;
    case CronTypeEnum.OTHER:
      crontab = form.crontab;
      break;
    default:
      break;
  }
  return {
    startTime: moment(form.startTime).format('YYYY-MM-DD HH:mm:ss'),
    endTime: moment(form.endTime).format('YYYY-MM-DD HH:mm:ss'),
    crontab,
    type
  };
};

/** 处理导出工作流
 * @param job 工作流
 * @returns 导出工作流
 *
 * @example
 * dealExportJob({name: 'test', code: 'test'}) // {name: 'test', code: 'test'}
 */
export const dealExportJob = (job: IJob) => {
  const jobJson = {
    name: job?.name,
    description: job?.description,
    code: job?.code,
    globalParams: job?.globalParams,
    scheduleConf: job?.scheduleConf
  };
  return JSON.stringify(jobJson, null, JSON_FORMAT);
};

// 权限检查
export const authCheck = (privileges: string[], auth: string) => {
  if (!privileges || !isArray(privileges)) {
    return false;
  }

  // 管理权限
  if (privileges.includes(Privilege.Manage)) {
    return true;
  }

  // 没有管理权限 但是需要管理权限
  if (auth === Privilege.Manage) {
    return false;
  }
  // 编辑权限
  if (privileges.includes(Privilege.Modify)) {
    return true;
  }
  // 没有编辑权限 但是需要编辑权限
  if (auth === Privilege.Modify) {
    return false;
  }

  return privileges.includes(auth);
};

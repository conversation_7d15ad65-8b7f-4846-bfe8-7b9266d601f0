/**
 * 新建 cron 弹窗
 */
import {detailJobFast, editJob} from '@api/job';
import {WorkspaceContext} from '@pages/index';
import {JobPriorityChineseMap, JobPriorityEnum} from '@pages/JobWorkflow/constants';
import {dealCronToForm, dealFormToCron} from '@pages/JobWorkflow/tools';
import {RULE} from '@utils/regs';
import {DatePicker, Form, Input, Modal, Select, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import moment from 'moment';
import React, {useContext, useEffect, useState} from 'react';
import {IJobModalProps} from '.';
import JobCronFormItem from '../FormItem/JobCronFormItem';
import JobCronShowTimeItem from '../FormItem/JobCronShowTimeItem';
import styles from './index.module.less';

const JobCronModal: React.FC<IJobModalProps> = (props: IJobModalProps) => {
  const {jobId, jobName, onSubmit} = props;
  const [isVisible, setIsVisible] = useState(false);
  const {workspaceId} = useContext(WorkspaceContext);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 提交表单
  const handleOk = useMemoizedFn(async () => {
    setLoading(true);

    const values = form.getFieldsValue();

    const {success} = await editJob({
      workspaceId,
      jobId,
      priority: values.priority,
      scheduleConf: dealFormToCron(values)
    });

    if (success) {
      toast.success({message: '更新成功', duration: 5});
      setIsVisible(false);
      onSubmit(true);
    }

    setLoading(false);
  });
  // 关闭弹窗
  const handleCancel = useMemoizedFn(() => {
    onSubmit(false);
    setIsVisible(false);
  });

  const initFn = useMemoizedFn(async () => {
    const {result} = await detailJobFast(workspaceId, jobId);

    const values = dealCronToForm(result.scheduleConf);
    form.setFieldsValue({
      name: jobName,
      priority: result.priority || JobPriorityEnum.MEDIUM,
      ...values
    });
    setLoading(false);
  });

  useEffect(() => {
    initFn();
    setIsVisible(true);
  }, []);

  return (
    <Modal
      closable={true}
      width={500}
      title={'调度策略'}
      visible={isVisible}
      onOk={handleOk}
      confirmLoading={loading}
      onCancel={handleCancel}
      className={styles['job-create-modal']}
    >
      <Form name="basic" layout="vertical" labelAlign="left" form={form} inputMaxWidth="500px">
        <Form.Item label="工作流名称" name="name">
          <Input
            disabled
            placeholder={RULE.workflowNameText}
            allowClear
            forbidIfLimit={true}
            limitLength={256}
          />
        </Form.Item>

        <Form.Item
          label="工作流优先级"
          name="priority"
          tooltip="提示：工作流在调度过程中的执行优先级，级别高的流程在执行队列中会优先执行，相同优先级的流程按照先进先出的顺序执行。"
        >
          <Select
            className="w-full"
            options={Object.entries(JobPriorityChineseMap).map(([key, value]) => ({
              value: key,
              label: value
            }))}
          />
        </Form.Item>

        <Form.Item label="起始时间" name="startTime">
          <DatePicker
            className="w-full"
            showTime
            showNow={false}
            clearIcon={false}
            disabledDate={(current) => current > moment(form.getFieldValue('endTime'))}
          />
        </Form.Item>

        <Form.Item label="终止时间" name="endTime">
          <DatePicker
            className="w-full"
            showTime
            showNow={false}
            clearIcon={false}
            disabledDate={(current) => current < moment(form.getFieldValue('startTime'))}
          />
        </Form.Item>

        <JobCronFormItem form={form} />

        <JobCronShowTimeItem form={form} />
      </Form>
    </Modal>
  );
};

export default JobCronModal;

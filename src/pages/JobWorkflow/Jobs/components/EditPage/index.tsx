import useUrlState from '@ahooksjs/use-url-state';
import {editJob, IJob, switchSchedule} from '@api/job';
import {JobScheduleStatusEnum, SelectJobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {WorkspaceContext} from '@pages/index';
import {IAppDispatch, IAppState} from '@store/index';
import {setFormIsDirty, setIsEditing, setRightDrawerTypeAndIndex, setSelectedNode} from '@store/workflow';
import {OperateType} from '@utils/enums';
import {Alert, Button, Col, Row, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {forwardRef, useContext, useImperativeHandle, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import LeaveFromModal from '../../DetailPage/components/LeaveFromModal';
import EditContent from './EditContent';
import {dealX6ToJson} from './EditContent/X6EditPage/tools/conversion';
import HeaderButton from './HeaderButton';
import RightDrawerPage from './RightDrawerPage';
import {getGraph, jobData} from './globalVar';

import {JobDetailPageRef} from '../../DetailPage';
import {checkJob} from './EditContent/X6EditPage/tools';
import styles from './index.module.less';
import AuthButton from '@components/AuthComponents/AuthButton';
import {authCheck} from '@pages/JobWorkflow/tools';
import {Privilege} from '@api/permission/type';

const JobEditPage = forwardRef<JobDetailPageRef, {jobObj: IJob; initJobDetail: () => void}>((props, ref) => {
  const {jobObj, initJobDetail} = props;
  const [{jobId, edit}] = useUrlState();
  const {workspaceId} = useContext(WorkspaceContext);
  const [leaveClickTime, setLeaveClickTime] = useState(0);

  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const isJsonPage = useSelector((state: IAppState) => state.workflowSlice.isJsonPage);
  const jobName = useSelector((state: IAppState) => state.workflowSlice.jobName);
  const formIsDirty = useSelector((state: IAppState) => state.workflowSlice.formIsDirty);

  const dispatch = useDispatch<IAppDispatch>();

  // 保存工作流
  const saveJob = useMemoizedFn(async (checkJsonFlag: boolean = false): Promise<boolean> => {
    const obj: IJob = jobData.value;
    // json 获取方式
    if (!isJsonPage) {
      const graph = getGraph();
      if (graph) {
        // 如果当前是可视化页面
        obj.code = dealX6ToJson(graph.toJSON());
      }
    }
    // 过滤掉 key 为空值的 globalParams
    obj.globalParams = obj.globalParams?.filter((item) => item.key || item.value);

    const {success, type, index, message, nodeId} = checkJob(obj, checkJsonFlag);
    if (!success) {
      toast.error({
        message,
        duration: 5
      });
      dispatch(setSelectedNode({id: nodeId, type: SelectJobNodeTypeEnum.CLICK}));

      setTimeout(() => {
        dispatch(setRightDrawerTypeAndIndex({type, index}));
      }, 10);
      return false;
    }
    try {
      const {success} = await editJob({
        ...obj,
        workspaceId,
        jobId
      });
      if (success) {
        toast.success({
          message: '保存成功',
          duration: 5
        });
        await initJobDetail();
        dispatch(setIsEditing(false));
        return true;
      }
    } catch (error) {
      console.log(error);
    }
    return false;
  });

  // 暴露 saveJob 方法
  useImperativeHandle(ref, () => ({
    saveJobFn(checkJsonFlag: boolean = false) {
      return saveJob(checkJsonFlag);
    }
  }));

  // 操作工作流
  const dealJobFn = (type: OperateType) => {
    switch (type) {
      case OperateType.EDIT:
        dispatch(setIsEditing(true));
        break;
      case OperateType.SAVE:
        saveJob();
        break;
      case OperateType.CANCEL:
        if (formIsDirty) {
          setLeaveClickTime(new Date().getTime());
        } else {
          dispatch(setIsEditing(false));
          initJobDetail();
        }
        break;
    }
  };

  // 关闭弹窗 flag 为 true 则保存
  const onClickAndRefresh = useMemoizedFn(async () => {
    // 关闭弹窗 不保存
    initJobDetail();
    dispatch(setIsEditing(false));
    dispatch(setFormIsDirty(false));
  });

  const closeSchedule = useMemoizedFn(async () => {
    const {success} = await switchSchedule(workspaceId, jobId, JobScheduleStatusEnum.OFF);
    if (success) {
      toast.success({
        message: '关闭调度成功',
        duration: 5
      });
      initJobDetail();
    }
  });

  return (
    <>
      <Row className={styles['edit-page-container']}>
        {/* 内容区域 */}
        <Col className={styles['content-box']}>
          <HeaderButton dealJobFn={dealJobFn} scheduleStatus={jobObj?.scheduleStatus} jobObj={jobObj} />
          {jobObj?.scheduleStatus && jobObj?.scheduleStatus !== JobScheduleStatusEnum.OFF && (
            <div className={styles['alert-box']}>
              <Alert
                message="工作流调度已开启，无法编辑，如需编辑调整工作流请先关闭调度。"
                type="info"
                showIcon
                action={
                  <AuthButton
                    isAuth={authCheck(jobObj?.privileges, Privilege.Modify)}
                    type="text"
                    className="default-btn"
                    onClick={closeSchedule}
                  >
                    立即关闭
                  </AuthButton>
                }
              />
            </div>
          )}
          <EditContent />
        </Col>

        {/* 右侧 作业基本信息 + 可视化预览 */}
        <RightDrawerPage />
      </Row>
      <LeaveFromModal
        leaveClickTime={leaveClickTime}
        isEditing={isEditing && formIsDirty}
        name={jobName}
        onClickAndRefresh={onClickAndRefresh}
        onSave={saveJob}
      />
    </>
  );
});

JobEditPage.displayName = 'JobEditPage';

export default JobEditPage;

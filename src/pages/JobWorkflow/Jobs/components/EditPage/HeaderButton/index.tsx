import {I<PERSON><PERSON>} from '@api/job';
import FormRadioButton from '@components/FormRadioButton';
import IconSvg from '@components/IconSvg';
import {
  JobScheduleStatusEnum,
  JobShowTypeEnum,
  RightDrawerTypeEnum,
  SelectJobNodeTypeEnum
} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {
  setIsJsonPage,
  setJobName,
  setJson,
  setRightDrawerTypeAndIndex,
  setSelectedNode
} from '@store/workflow';
import {OperateType} from '@utils/enums';
import {Button, Input, Popover} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import ImportantJsonModal from '../../ImportantJson';
import {dealX6ToJson} from '../EditContent/X6EditPage/tools/conversion';
import {getGraph, jobData, nodeMap} from '../globalVar';
import HeaderParams from './HeaderParams';
import styles from './index.module.less';
import useWorkspaceAuth from '@hooks/useWorkspaceAuth';
import {Privilege} from '@api/permission/type';
import AuthButton from '@components/AuthComponents/AuthButton';
import {authCheck} from '@pages/JobWorkflow/tools';
import {TooltipType} from '@components/AuthComponents/constants';

const HeaderButton: React.FC<{
  dealJobFn: (type: OperateType) => void;
  scheduleStatus: JobScheduleStatusEnum;
  jobObj: IJob;
}> = ({dealJobFn, scheduleStatus, jobObj}) => {
  // 权限列表
  const authList = useWorkspaceAuth([Privilege.WorkflowImport]);
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const isJsonPage = useSelector((state: IAppState) => state.workflowSlice.isJsonPage);
  const dispatch = useDispatch();
  const [showSearchNode, setShowSearchNode] = useState<boolean>(false);

  // 节点列表 点击的时候 从全局变量 获取
  const [nodeList, setNodeList] = useState<{id: string; name: string}[]>([]);

  const getNodeList = () => {
    const list = [];
    nodeMap.forEach((node) => {
      if ('type' in node) {
        list.push({id: node.id, name: node.name});
      }
    });
    setNodeList(list);
  };

  // 导入工作流
  const importWorkflow = useMemoizedFn((job: IJob) => {
    dispatch(setJson(job?.code));

    if (job.name) {
      dispatch(setJobName(job.name));
    }

    const changeName = ['name', 'description', 'globalParams', 'scheduleConf'];
    changeName.forEach((item) => {
      if (job[item]) {
        jobData.value[item] = job[item];
      }
    });
    dispatch(setRightDrawerTypeAndIndex({type: RightDrawerTypeEnum.JOB_CONFIG, index: 0}));
  });

  // 切换可视化与json
  const changeShowType = useMemoizedFn((value: JobShowTypeEnum) => {
    const isJson = value === JobShowTypeEnum.JSON;
    if (!isJson) {
      // json 切可视化  设置 redux 的 json配置
      dispatch(setJson(jobData.value.code));
    } else {
      // 可视化 切 json  构建 json 数据
      const graph = getGraph();
      if (graph) {
        // 如果当前是可视化页面
        const json = dealX6ToJson(graph.toJSON());
        jobData.value.code = json;
        dispatch(setJson(json));
      }
    }

    dispatch(setIsJsonPage(isJson));
  });

  const changeSearchNode = useMemoizedFn((value: string) => {
    const node = nodeList.find((item) => item.name === value);
    if (node?.id) {
      dispatch(setSelectedNode({id: node.id, type: SelectJobNodeTypeEnum.SEARCH}));

      // 选中节点后，自动居中
      const graph = getGraph();
      const cell = graph.getCellById(node.id);
      graph.centerCell(cell);
    } else {
      dispatch(setSelectedNode({id: '', type: SelectJobNodeTypeEnum.SEARCH}));
    }
  });

  return (
    <>
      <div className={styles['header-btn']}>
        <div className={styles['left-btn']}>
          {!isEditing ? (
            <>
              <AuthButton
                isAuth={authCheck(jobObj?.privileges, Privilege.Modify)}
                disabled={scheduleStatus !== JobScheduleStatusEnum.OFF}
                size="small"
                onClick={() => dealJobFn(OperateType.EDIT)}
                icon={<IconSvg type="edit" />}
                type="text"
              >
                编辑
              </AuthButton>
            </>
          ) : (
            <>
              <Button
                size="small"
                onClick={() => dealJobFn(OperateType.SAVE)}
                icon={<IconSvg type="workflow-save" />}
                type="text"
              >
                保存
              </Button>
              <Button
                size="small"
                onClick={() => dealJobFn(OperateType.CANCEL)}
                icon={<IconSvg type="workflow-quit" />}
                type="text"
              >
                退出
              </Button>

              <ImportantJsonModal onChange={importWorkflow}>
                <AuthButton
                  size="small"
                  icon={<IconSvg type="workflow-import" />}
                  type="text"
                  isAuth={authList[Privilege.WorkflowImport]}
                  tooltipType={TooltipType.Function}
                >
                  工作流导入
                </AuthButton>
              </ImportantJsonModal>
            </>
          )}
        </div>
        <div className={styles['right-btn']}>
          {!isJsonPage && (
            <div className={styles['search-btn']}>
              {showSearchNode ? (
                <Input.AutoComplete
                  autoFocus
                  showSearch
                  className={styles['search-input']}
                  size="small"
                  options={nodeList.map((item) => ({label: item.name, value: item.name}))}
                  onSelect={(value) => {
                    changeSearchNode(value);
                  }}
                  filterOption={(input, option) => {
                    return option!.value?.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  <Input
                    size="small"
                    onFocus={() => {
                      getNodeList();
                    }}
                    allowClear
                    onChange={(e) => {
                      changeSearchNode(e.target.value);
                    }}
                    onBlur={(e) => {
                      if (!e.target?.value) {
                        setShowSearchNode(false);
                      }
                    }}
                    suffix={<IconSvg type="workflow-search" fill="none" />}
                  />
                </Input.AutoComplete>
              ) : (
                <Button
                  size="small"
                  icon={<IconSvg type="workflow-search" fill="none" />}
                  type="text"
                  onClick={() => {
                    setShowSearchNode(true);
                  }}
                ></Button>
              )}
            </div>
          )}

          <div className={styles['param-btn']}>
            <Popover content={<HeaderParams />} trigger="click" destroyTooltipOnHide={true}>
              <Button size="small" icon={<IconSvg type="workflow-save" />} type="text">
                参数
              </Button>
            </Popover>
          </div>

          <div className={styles['show-type-btn']}>
            <FormRadioButton
              options={[
                {label: '可视化', value: JobShowTypeEnum.X6, icon: 'workflow-x6'},
                {label: 'JSON', value: JobShowTypeEnum.JSON, icon: 'workflow-json'}
              ]}
              activeValue={isJsonPage ? JobShowTypeEnum.JSON : JobShowTypeEnum.X6}
              onChange={(value) => {
                changeShowType(value as JobShowTypeEnum);
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default HeaderButton;

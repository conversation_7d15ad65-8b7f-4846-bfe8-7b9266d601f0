.dnd-content {
  .operator-name {
    margin-left: 0px;
    height: 28px;
    font-size: 12px;
    line-height: 28px;
    color: #151b26;
    display: flex;
    align-items: center;
    gap: 6px;
    .operator-view {
      opacity: 0;
    }
    &:hover {
      .operator-view {
        opacity: 1;
      }
    }
    .operator-view:hover {
      color: #2468f2;
    }
  }
  .custom-operator-name {
    margin-left: 0px;

    &:hover {
      cursor: pointer;
      background-color: #e8e9eb;
    }
  }
}
.header {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  margin-bottom: 12px;
  margin-top: 12px;
  .icon-div {
    :hover {
      color: #2468f2;
      cursor: pointer;
    }
  }
}
.filter-tree {
  max-height: 300px;
  min-width: 200px;
  overflow: auto;
}

.input-auto {
  margin-bottom: 9px;
}

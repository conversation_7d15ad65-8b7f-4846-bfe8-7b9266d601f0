import {Graph} from '@antv/x6';
import {MiniMap} from '@antv/x6-plugin-minimap';
import {Portal, register} from '@antv/x6-react-shape';
import React, {useContext, useEffect, useMemo, useRef, useState} from 'react';

import {Dnd} from '@antv/x6-plugin-dnd';

import {getOperatorLatestList} from '@api/metaRequest';
import {
  GraphOptions,
  JobNodeTypeEnum,
  LeftDragTypeEnum,
  NODE_SIZE,
  RightDrawerTypeEnum,
  SelectJobNodeTypeEnum,
  SPLIT_STR,
  X6BtnArrTypeEnum,
  X6PageTypeEnum,
  X6ShapeTypeEnum
} from '@pages/JobWorkflow/constants';
import store, {IAppDispatch, IAppState} from '@store/index';
import {setIsJsonPage, setJson, setRightDrawerType, setSelectedNode} from '@store/workflow';
import {toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {Provider, useDispatch, useSelector} from 'react-redux';
import {customOperatorDetailMap, operatorDetailMap, setGraph} from '../../globalVar';
import ResizablePanel from './components/ResizablePanel';
import styles from './index.module.less';
import {autoLayoutFn, dealJsonToX6, hasCycle, initEventListeners} from './tools';
import {OperatorNodeComponent} from './X6Component/OperatorNodeComponent';
import {TaskNodeComponent} from './X6Component/TaskNodeComponent';
import {X6BtnArr} from './X6Component/X6BtnArr';
import {
  OperatorGroupNodeComponent,
  SimpleNodeOperatorView,
  SimpleNodeTaskView
} from './X6Component/X6Components';
import {X6_LINE, X6_PORTS_OPERATOR, X6_PORTS_TASK} from './x6DefaultConfig';
import X6DragPage from './X6DragPage';
import {WorkspaceContext} from '@pages/index';
import {IJsonNodeData, IJsonOperatorData} from './type';
import {operatorDetailList} from '@api/meta/operate';
const X6ReactPortalProvider = Portal.getProvider(); // 注意，一个 graph 只能申明一个 portal provider

// 注册任务节点
register({
  inherit: 'react-shape',
  shape: X6ShapeTypeEnum.TASK,
  width: NODE_SIZE[X6ShapeTypeEnum.TASK].width,
  height: NODE_SIZE[X6ShapeTypeEnum.TASK].height,
  // 监听数据变化
  effect: ['data'],
  component: TaskNodeComponent,
  ports: X6_PORTS_TASK
});

// 注册算子节点
register({
  inherit: 'react-shape',
  shape: X6ShapeTypeEnum.OPERATOR,
  width: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width,
  height: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height,
  // 监听数据变化
  effect: ['data'],
  component: OperatorNodeComponent,
  ports: X6_PORTS_OPERATOR
});

// 注册算子组节点
register({
  inherit: 'react-shape',
  shape: X6ShapeTypeEnum.OPERATOR_GROUP,
  component: OperatorGroupNodeComponent,
  // 监听数据变化
  effect: ['data']
});

// Graph.registerRouter('autoRouter', autoRouter);

// 定义类型
interface IX6Props {
  jsonStr?: string;
  pageType?: X6PageTypeEnum;
}
// json 转为 x6 图形
const X6EditPage: React.FC<IX6Props> = ({jsonStr, pageType}) => {
  // 是否只读 非编辑页面
  const isReadOnly = useMemo(() => pageType !== X6PageTypeEnum.JOB_EDIT, [pageType]);

  const jsonData = useSelector((state: IAppState) => state.workflowSlice.jsonData);
  const [showJsonStr, setShowJsonStr] = useState<string | undefined>(jsonData);

  // 详情展示json 使用组件传入值
  useEffect(() => {
    if (jsonStr) {
      setShowJsonStr(jsonStr);
    } else {
      setShowJsonStr(jsonData);
    }
  }, [jsonStr, jsonData]);

  const editNodeData = useSelector((state: IAppState) => state.workflowSlice.editNodeData);
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const leftDragType = useSelector((state: IAppState) => state.workflowSlice.leftDragType);

  const dispatch = useDispatch<IAppDispatch>();
  const graphRef = useRef<Graph>();
  const dndRef = useRef<Dnd>();
  const containerRef = useRef<HTMLDivElement>(null);
  const minimapRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(100);

  // 工作空间id
  const {workspaceId} = useContext(WorkspaceContext);

  // 加载 json 数据到 x6 dealJsonToX6
  const loadJson = () => {
    if (graphRef.current) {
      if (!showJsonStr) {
        graphRef.current.fromJSON({cells: []});
        return;
      }
      try {
        const obj: any = JSON.parse(showJsonStr);
        const cells = dealJsonToX6(obj);
        graphRef.current.fromJSON(cells);

        setTimeout(() => {
          // 缩放至合适大小
          graphRef.current?.zoomToFit(GraphOptions.zoomToFit);
        }, 100);
      } catch (error) {
        // 解析失败 展示json
        dispatch(setSelectedNode({id: null, type: SelectJobNodeTypeEnum.NULL}));
        dispatch(setRightDrawerType(RightDrawerTypeEnum.JOB_CONFIG));
        dispatch(setJson(showJsonStr));
        dispatch(setIsJsonPage(true));
        toast.error({
          message: '解析 JSON 失败',
          duration: 3
        });
      }
    }
  };
  // 初始化 x6 图形
  const initGraph = () => {
    const graph = new Graph({
      container: containerRef.current!,
      background: {
        color: '#FBFBFC'
      },
      grid: true, // 网格
      scaling: {min: 0.01, max: 5},
      mousewheel: {enabled: true, factor: 1.05},
      autoResize: true, // 自适应大小
      panning: true, // 平移 与滚动画布有冲突
      embedding: {
        enabled: true, // 自动吸附
        findParent({node}) {
          const data = node.getData();
          if (node.shape === X6ShapeTypeEnum.OPERATOR) {
            const arr = [this.getCellById(data.parentId)];
            return arr;
          }
          return [];
        }
      },
      connecting: {
        // 不允许空白连线
        allowBlank: false,
        // 不允许循环连线
        allowLoop: false,
        // 不允许 在相同的起始节点和终止之间创建多条边
        allowMulti: false,
        // 不允许 连线
        allowEdge: false,
        router: {
          name: 'er',
          args: {
            offset: 40
          }
        },
        connector: {name: 'rounded'},
        createEdge() {
          return graph.createEdge({
            attrs: X6_LINE
          });
        },
        validateConnection({sourceCell, targetCell}) {
          const sourceData = sourceCell.getData();
          const targetData = targetCell.getData();

          // 不允许 环
          if (hasCycle(graphRef.current!, sourceCell.id, targetCell.id)) {
            return false;
          }

          // 算子 只允许内部连接
          if (
            sourceData.type === JobNodeTypeEnum.OPERATOR_NODE &&
            targetData.type === JobNodeTypeEnum.OPERATOR_NODE &&
            sourceData.parentId === targetData.parentId
          ) {
            return true;
          }

          // 任务 可以相互连接
          if (
            sourceData.type !== JobNodeTypeEnum.OPERATOR_NODE &&
            targetData.type !== JobNodeTypeEnum.OPERATOR_NODE
          ) {
            return true;
          }
          return false;
        },

        validateMagnet({magnet}) {
          // 只允许标记了 magnet 的元素能触发连线
          return magnet.getAttribute('magnet') !== 'false';
        }
      }
    });

    setGraph(graph);

    initEventListeners({dispatch, setScale, pageType});

    // 缩略图
    graph.use(
      new MiniMap({
        container: minimapRef.current!,
        graphOptions: {
          createCellView(cell) {
            if (cell.isNode() && cell.shape === X6ShapeTypeEnum.OPERATOR) {
              return SimpleNodeOperatorView;
            }
            if (cell.isNode() && cell.shape === X6ShapeTypeEnum.TASK) {
              return SimpleNodeTaskView;
            }
            return null;
          }
        },
        width: 132,
        maxScale: 1,
        height: 84,
        padding: 10
      })
    );

    // 拖拽
    dndRef.current = new Dnd({
      getDragNode: (node) => node.clone({keepId: true}),
      getDropNode: (node) => node.clone({keepId: true}),
      target: graph,
      scaled: false
    });

    graphRef.current = graph;
  };

  // 初始化内置算子详情
  const initOperatorDetail = async () => {
    const res = await getOperatorLatestList(workspaceId);
    res.result?.versions.forEach((item) => {
      operatorDetailMap.set(
        [item.catalogName, item.schemaName, item.operatorName, item.name].join(SPLIT_STR),
        item
      );
    });
  };

  // 初始化自定义算子详情
  const initCustomOperatorDetail = useMemoizedFn(async (jsonStr: string) => {
    try {
      const json = JSON.parse(jsonStr);
      const params: {catalogName: string; schemaName: string; operatorName: string; versionName: string}[] =
        [];
      json.taskDefinitionList.forEach((item: IJsonNodeData) => {
        if (item.operatorList) {
          item.operatorList.forEach((operator: IJsonOperatorData) => {
            const version = operator?.metaData?.version;
            params.push({...operator.metaData, versionName: version});
          });
        }
      });

      if (params.length > 0) {
        const res = await operatorDetailList(workspaceId, params);
        res.result?.versions.forEach((item) => {
          customOperatorDetailMap.set(
            [item.catalogName, item.schemaName, item.operatorName, item.name].join(SPLIT_STR),
            item
          );
        });
      }
    } catch (error) {
      console.error(error);
    }
  });

  // 初始化 x6 图形
  useEffect(() => {
    initGraph();
    initOperatorDetail();

    return () => {
      // 延迟销毁，避免销毁时，页面大小变化，导致图形对象销毁失败
      setTimeout(() => {
        console.log('销毁 X6 Graph');
        graphRef.current?.dispose(); // 清理 Graph 实例
        graphRef.current = null;
        setGraph(null);
        dispatch(setSelectedNode({id: null, type: SelectJobNodeTypeEnum.NULL}));
      }, 10);
    };
  }, []);

  // 监听 json 数据变化，重新加载 x6 图形
  useEffect(() => {
    loadJson();
    // json 变化 初始化算子详情
    initCustomOperatorDetail(showJsonStr);
  }, [showJsonStr]);

  // 监听 isEditing 数据变化，更新 x6 图形
  useEffect(() => {
    graphRef.current.options.interacting = isEditing && pageType === X6PageTypeEnum.JOB_EDIT;
  }, [isEditing, pageType]);

  // 缩放
  const onChangeZoom = useMemoizedFn((num: number) => {
    graphRef.current?.zoomTo(num / 100);
  });

  useEffect(() => {
    if (editNodeData) {
      const node = graphRef.current?.getCellById(editNodeData.id);
      if (node) {
        node.setData({
          ...node.getData(),
          name: editNodeData.name
        });
      }
    }
  }, [editNodeData]);

  // 点击按钮
  const clickBtnArr = useMemoizedFn((type: X6BtnArrTypeEnum) => {
    // 自动布局
    if (type === X6BtnArrTypeEnum.AUTO_LAYOUT) {
      const json = graphRef.current?.toJSON();
      const result: any = autoLayoutFn(json.cells);
      graphRef.current?.fromJSON(result);
      graphRef.current?.zoomToFit(GraphOptions.zoomToFit);
    } else if (type === X6BtnArrTypeEnum.FOCUS_CENTER) {
      graphRef.current?.zoomToFit(GraphOptions.zoomToFit);
    }
  });

  return (
    <div className={styles['x6-main'] + (!isReadOnly ? '' : ' ' + styles['readonly-x6'])}>
      <Provider store={store}>
        <X6ReactPortalProvider />
      </Provider>
      {/* 拖拽页面 */}
      <ResizablePanel
        defaultWidth={leftDragType === LeftDragTypeEnum.OPERATOR ? 300 : 160}
        className={styles['x6-drag-page']}
        // 编辑页面 可拖拽 可编辑
        style={{display: isEditing && pageType === X6PageTypeEnum.JOB_EDIT ? 'block' : 'none'}}
      >
        <X6DragPage graph={graphRef} dnd={dndRef} />
      </ResizablePanel>
      <div className={styles['x6-content']}>
        <div ref={containerRef} className="w-full h-full" />
        <div className={styles['minimap-container']} ref={minimapRef} />

        <div className={styles['zoom-btn']}>
          <X6BtnArr value={scale} onChange={onChangeZoom} clickFn={clickBtnArr} />
        </div>
      </div>
    </div>
  );
};

export default React.memo(X6EditPage);

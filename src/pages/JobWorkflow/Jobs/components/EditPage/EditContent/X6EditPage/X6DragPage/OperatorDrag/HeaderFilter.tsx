import React, {Key, useEffect, useRef, useState} from 'react';
import IconSvg from '@components/IconSvg';
import styles from './index.module.less';
import {Badge, Input, Popover, Tree, TreeSelect} from 'acud';
import {IOperatorTreeData} from '.';
import {operatorDetailMap} from '../../../../globalVar';
import {IOperCategoryEnum, IOperVersionOneRes} from '@api/metaRequest';
import {
  JobNodeTypeEnum,
  OperatorCategoryChineseMap,
  OperatorFieldChineseMap,
  OperatorFieldEnum,
  SPLIT_STR
} from '@pages/JobWorkflow/constants';
import {useMemoizedFn} from 'ahooks';

const customOperator = {
  title: '自定义算子',
  key: 'custom' + SPLIT_STR + 'custom'
};
//定义类型
interface IHeaderFilterProps {
  // 处理算子节点展示
  dealOperatorNode?: (item: IOperVersionOneRes, filterName: string) => IOperatorTreeData;
  // 处理算子树
  changeOperatorTree?: (tree: IOperatorTreeData[]) => void;
}

// 算子拖拽组件
const HeaderFilter: React.FC<IHeaderFilterProps> = ({dealOperatorNode, changeOperatorTree}) => {
  const [operatorNum, setOperatorNum] = useState<number>(0);

  // 过滤的树组件
  const [filterTree, setFilterTree] = useState<IOperatorTreeData[]>([]);

  // 树形过滤
  const [filterTreeType, setFilterTreeType] = useState<string[]>([]);
  // 输入框过滤
  const [filterInputString, setFilterInputString] = useState<string>();

  // 搜索条件
  const [options, setOptions] = useState<{value: string}[]>([]);

  const initOperatorMap = useMemoizedFn(
    (inputString = '', treeType = []): Map<string, IOperVersionOneRes[]> => {
      const arr = Array.from(operatorDetailMap.values());

      const map = new Map<string, IOperVersionOneRes[]>();
      const optionArr = [];
      // 所有算子
      arr.forEach((item) => {
        const category = item.category || IOperCategoryEnum.Others;
        const field = item.field || OperatorFieldEnum.GENERAL;
        const mapKey = category + SPLIT_STR + field;

        const filterFlag = treeType?.includes(mapKey) || treeType?.length === 0;
        const searchFlag = !inputString || item.operatorAlias.includes(inputString);
        if (filterFlag && searchFlag) {
          if (map.has(mapKey)) {
            // 算子类别 和 算子 过滤类型 作为 key 存储
            map.get(mapKey).push(item);
          } else {
            map.set(mapKey, [item]);
          }
          optionArr.push({value: item.operatorAlias});
        }
      });
      // 添加自定义算子名称
      optionArr.push({value: customOperator.title});
      setOptions(optionArr);
      setOperatorNum(optionArr.length);

      return map;
    }
  );

  // 初始化算子详情
  const initOperatorDetail = useMemoizedFn((inputString = '', treeType = []) => {
    const map = initOperatorMap(inputString, treeType);
    const treeArr: IOperatorTreeData[] = [];
    // 算子类别
    Object.entries(OperatorCategoryChineseMap).forEach(([key, value]) => {
      const category: IOperatorTreeData = {
        title: value,
        key: key,
        children: []
      };

      // 算子  过滤类型
      Object.entries(OperatorFieldChineseMap).forEach(([operatorFieldKey, operatorFieldValue]) => {
        const operatorKey = key + SPLIT_STR + operatorFieldKey;
        if (map.has(operatorKey)) {
          category.children.push({
            title: operatorFieldValue,
            key: operatorKey,
            children: map.get(operatorKey)?.map((item) => {
              return dealOperatorNode(item, inputString);
            })
          });
        }
      });
      if (category.children.length > 0) {
        treeArr.push(category);
      }
    });
    // 没有过滤 就添加自定义算子
    if (
      customOperator.title.includes(inputString) &&
      (treeType?.length === 0 || treeType?.includes(customOperator.key))
    ) {
      treeArr.unshift({
        title: '自定义算子',
        key: 'custom',
        children: [
          dealOperatorNode(
            {
              operatorAlias: '自定义算子'
            } as IOperVersionOneRes,
            filterInputString
          )
        ]
      });
    }

    changeOperatorTree(treeArr);
  });

  // 初始化 或者 搜索条件变更
  useEffect(() => {
    initOperatorDetail(filterInputString, filterTreeType);
  }, [filterInputString, filterTreeType, initOperatorDetail]);

  // 初始化 过滤树 ，只展示 有数据的结构
  const initFilterTree = useMemoizedFn(() => {
    const map = initOperatorMap();
    const treeFilterArr: IOperatorTreeData[] = [];
    // 算子类别
    Object.entries(OperatorCategoryChineseMap).forEach(([key, value]) => {
      // 算子  过滤类型
      const category: IOperatorTreeData = {
        title: value,
        key: key,
        children: []
      };

      // 算子  过滤类型
      Object.entries(OperatorFieldChineseMap).forEach(([operatorFieldKey, operatorFieldValue]) => {
        const operatorKey = key + SPLIT_STR + operatorFieldKey;
        if (map.has(operatorKey)) {
          category.children.push({
            title: operatorFieldValue,
            key: operatorKey,
            children: []
          });
        }
      });
      if (category.children.length > 0) {
        treeFilterArr.push(category);
      }
    });

    treeFilterArr.unshift(customOperator);
    setFilterTree(treeFilterArr);
  });

  useEffect(() => {
    initFilterTree();
  }, [initFilterTree]);

  const onSelect = (v) => {
    console.log('onSelect', v);
    // 只需要二级树结构
    setFilterTreeType(v.map((item) => String(item)).filter((item) => item.includes(SPLIT_STR)));
  };
  // 查询算子树结构
  const onSearch = (v) => {
    console.log('onSearch', v);
    setFilterInputString(v);
  };
  return (
    <>
      <div className={styles['header']}>
        <div className={styles['title']}>算子节点({operatorNum})</div>
        <Popover
          content={
            <div className={styles['filter-tree']}>
              <Tree treeData={filterTree} checkable onCheck={onSelect} />
            </div>
          }
          trigger="click"
        >
          <div className={styles['icon-div']}>
            <Badge count={filterTreeType.length}>
              <IconSvg fill="none" type="filter"></IconSvg>
            </Badge>
          </div>
        </Popover>
      </div>
      <div className={styles['input-auto']}>
        <Input.AutoComplete
          value={filterInputString}
          options={options}
          onSelect={onSearch}
          onSearch={onSearch}
          placeholder="请输入关键字检索算子"
        />
      </div>
    </>
  );
};

export default React.memo(HeaderFilter);

import FlexDrawerArr from '@components/FlexDrawerArr';
import IconSvg from '@components/IconSvg';
import {IAppState} from '@store/index';
import React, {useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import OperatorBaseParams from './OperatorBaseParams';
import {nodeMap} from '../../globalVar';
import {IJsonNodeData, IJsonOperatorData} from '../../EditContent/X6EditPage/type';
import {CatalogType} from '@api/metaRequest';
import OperatorCustomBaseParams from './OperatorCustomBaseParams';

const TaskConfigPage: React.FC = () => {
  // 是否编辑
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const selectedNode: IJsonOperatorData = useMemo(() => {
    return nodeMap.get(selectedNodeId) as IJsonOperatorData;
  }, [selectedNodeId]);
  useEffect(() => {
    if (selectedNodeId) {
      setActiveIndex(0);
    }
  }, [selectedNodeId]);

  return (
    <>
      {/* 右侧 作业基本信息 + 可视化预览 */}
      <FlexDrawerArr
        activeIndex={activeIndex}
        changeIndex={setActiveIndex}
        changeVisible={(flag) => {
          if (!flag) {
            setActiveIndex(undefined);
          }
        }}
        iconTitleArr={[{icon: <IconSvg type="workflow-drawer-base-info" />, title: '基本信息'}]}
      >
        <div>
          {selectedNode.metaData?.catalogName === CatalogType.SYSTEM ? (
            <OperatorBaseParams />
          ) : (
            <OperatorCustomBaseParams />
          )}
        </div>
        <div></div>
      </FlexDrawerArr>
    </>
  );
};

export default TaskConfigPage;

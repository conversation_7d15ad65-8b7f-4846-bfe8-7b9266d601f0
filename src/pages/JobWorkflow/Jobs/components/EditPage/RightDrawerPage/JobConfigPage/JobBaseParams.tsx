import {IJob} from '@api/job';
import {Clipboard} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {
  JobExecutionTypeChineseMap,
  JobExecutionTypeEnum,
  JobFailureStrategyChineseMap,
  JobFailureStrategyEnum
} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {setFormIsDirty, setJobName} from '@store/workflow';
import {RULE} from '@utils/regs';
import {Button, Form, Input, InputNumber, Radio, Select} from 'acud';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {jobData} from '../../globalVar';
import styles from '../index.module.less';
import AlertStrategyList from '../TaskConfigPage/TaskBaseParams/components/formItem/AlertStrategyList/AlertStrategyList';

const JobBaseParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const jobName = useSelector((state: IAppState) => state.workflowSlice.jobName);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [jobDetail, setJobDetail] = useState<IJob | null>(null);
  // 初始化表单
  useEffect(() => {
    setJobDetail(jobData.value);
    form.setFieldsValue(jobData.value);
    form.validateFields({dirty: false});
  }, []);

  useEffect(() => {
    form.setFieldsValue({name: jobName});
    setJobDetail(jobData.value);
  }, [jobName]);

  // 修改表单 更新全局变量
  const changeForm = (_: any, values: IJob) => {
    jobData.value = {...jobData.value, ...values};

    // 设置表单脏
    dispatch(setFormIsDirty(true));
  };

  // 修改工作流名称
  const changeName = (value: string) => {
    dispatch(setJobName(value));
  };

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        className={styles['form-container']}
        labelAlign="left"
        layout="horizontal"
        name="basic"
        form={form}
        labelWidth={70}
        colon={false}
        onValuesChange={changeForm}
      >
        <div className={'form-title'}>
          <IconSvg size={16} type="workflow-detail" />
          <span className={'form-title-text'}>工作流信息</span>
        </div>
        <Form.Item
          label="工作流名称"
          name="name"
          rules={[
            {required: isEditing ? true : false, message: '请输入工作流名称'},
            {pattern: RULE.workflowName, message: RULE.workflowNameText}
          ]}
        >
          <EditableContent isEditing={isEditing} onChange={(e) => changeName(e.target.value)}>
            <Input forbidIfLimit={true} limitLength={256} placeholder="请输入描述" allowClear />
          </EditableContent>
        </Form.Item>
        <Form.Item label="工作流 ID">
          {jobDetail?.jobId}
          <Clipboard text={jobDetail?.jobId} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item>
        <Form.Item label="创建时间">{jobDetail?.createdAt}</Form.Item>
        <Form.Item label="修改时间">{jobDetail?.updatedAt || '-'}</Form.Item>
        <Form.Item label="创建人">{jobDetail?.createUser || '-'}</Form.Item>
        <Form.Item label="最后修改人">{jobDetail?.updateUser || '-'}</Form.Item>
        <Form.Item label="描述" name="description">
          <EditableContent isEditing={isEditing}>
            <Input.TextArea forbidIfLimit={true} limitLength={500} placeholder="请输入描述" allowClear />
          </EditableContent>
        </Form.Item>

        <div className={'form-title'}>
          <IconSvg size={16} type="workflow-detail" />
          <span className={'form-title-text'}>运行配置</span>
        </div>

        <Form.Item
          label="执行策略"
          name="executionType"
          initialValue={JobExecutionTypeEnum.PARALLEL}
          tooltip={
            <>
              提示：
              <br />
              1. 并行执行：如果对于同一个工作流定义，同时有多个工作流实例，则全部并行执行工作流实例。
              <br />
              2.串行执行：如果对于同一个工作流定义，同时有多个工作流实例，可同时执行的工作流实例由最大数并行数决定，超过最大并行数则串行执行工作流实例。
            </>
          }
        >
          <EditableContent isEditing={isEditing} dealValue={(value) => JobExecutionTypeChineseMap[value]}>
            <Select
              className="w-full"
              options={Object.entries(JobExecutionTypeChineseMap).map(([key, value]) => ({
                label: value,
                value: key
              }))}
            ></Select>
          </EditableContent>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues['executionType'] !== curValues['executionType']}
        >
          {({getFieldValue}) => {
            const executionType = getFieldValue(['executionType']);
            if (executionType === JobExecutionTypeEnum.SERIAL_WAIT) {
              return (
                <Form.Item
                  label="最大并行数"
                  name="maxConcurrency"
                  tooltip="超过最大并行数据的工作流实例，则排队等待执行，如果最大并行数为1，则所有工作流实例依次串行执行。"
                >
                  <EditableContent isEditing={isEditing}>
                    <InputNumber className="w-full" min={1} precision={0} placeholder="请输入" />
                  </EditableContent>
                </Form.Item>
              );
            }
            return null;
          }}
        </Form.Item>

        <Form.Item
          label="失败策略"
          name="failureStrategy"
          initialValue={JobFailureStrategyEnum.END}
          tooltip="提示：当某一个任务节点执行失败时，其他并行的任务节点需要执行的策略。”继续“表示：某一任务失败后，其他任务节点正常执行；”结束“表示：终止所有正在执行的任务，并终止整个流程"
        >
          <EditableContent isEditing={isEditing} dealValue={(value) => JobFailureStrategyChineseMap[value]}>
            <Radio.Group>
              {Object.entries(JobFailureStrategyChineseMap).map(([key, value]) => {
                return (
                  <Radio key={key} value={key}>
                    {value}
                  </Radio>
                );
              })}
            </Radio.Group>
          </EditableContent>
        </Form.Item>

        {/* 等待8月份放开测试 */}
        {/* <Form.Item name="jobAlertStrategyList">
          <AlertStrategyList />
        </Form.Item> */}
      </Form>
    </>
  );
};

export default JobBaseParams;

import {getWorkspaceFileResult} from '@api/WorkArea';
import EditableContent from '@components/EditableContent';
import FilePathSelectWorkarea, {FileNodeTypeEnum} from '@components/FilePathSelectWorkarea';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {Form} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React, {useContext} from 'react';
import {useSelector} from 'react-redux';

const NotebookParams: React.FC<{form?: FormInstance}> = ({form}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {workspaceId} = useContext(WorkspaceContext);
  return (
    <>
      <Form.Item
        label="运行代码路径"
        name="jupyterFilePath"
        rules={
          isEditing
            ? [
                {required: true, message: '请输入运行代码路径'},
                {
                  validator: (rule, value, callback) => {
                    getWorkspaceFileResult({path: value, workspaceId}).then((res) => {
                      if (!value) {
                        callback();
                        return;
                      }
                      if (res.result?.message) {
                        callback(res.result.message);
                      } else if (res.result?.nodeType !== FileNodeTypeEnum.NOTEBOOK) {
                        callback('输入路径为不是Notebook，请选择Notebook');
                      } else {
                        callback();
                      }
                    });
                  }
                }
              ]
            : []
        }
      >
        <EditableContent isEditing={isEditing}>
          <FilePathSelectWorkarea selectNodeType={FileNodeTypeEnum.NOTEBOOK} />
        </EditableContent>
      </Form.Item>
    </>
  );
};

export default NotebookParams;

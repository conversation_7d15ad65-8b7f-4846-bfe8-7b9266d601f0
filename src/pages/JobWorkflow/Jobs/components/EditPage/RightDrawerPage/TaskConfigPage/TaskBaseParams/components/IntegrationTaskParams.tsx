import {getIntegration<PERSON><PERSON><PERSON><PERSON>, getJob<PERSON>n<PERSON><PERSON><PERSON>, getJobDetails} from '@api/integration';
import {JobConfigStatus, JobDetailRes, JobType} from '@api/integration/type';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import RemoteSelect from '@components/RemoteSelect';
import {WorkspaceContext} from '@pages/index';
import {JobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import urls from '@utils/urls';
import {Form} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../../globalVar';
import {Privilege} from '@api/permission/type';
import {authCheck} from '@pages/JobWorkflow/tools';

const IntegrationTaskParams: React.FC<{form?: FormInstance; type: JobNodeTypeEnum}> = ({form, type}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const {workspaceId} = useContext(WorkspaceContext);
  const [jobName, setJobName] = useState<React.ReactNode>('');

  const jobType = useMemo(() => {
    return type === JobNodeTypeEnum.FILE_INTEGRATION_TASK ? JobType.File : JobType.Batch;
  }, [type]);

  const nameIcon = useMemoizedFn((obj: JobDetailRes, id: string) => {
    const label = obj?.name;
    return (
      <div
        style={{
          width: '100%',
          display: 'flex',
          gap: 8,
          alignItems: 'center'
        }}
      >
        <Ellipsis tooltip={label}>{label}</Ellipsis>
        <IconSvg
          className="cursor-pointer"
          color="#2468f2"
          type="open"
          size={16}
          onClick={(e) => {
            const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(obj.status);
            if (type === JobNodeTypeEnum.FILE_INTEGRATION_TASK) {
              window.open(
                `#${urls.fileCollectDetail}?jobId=${id}&workspaceId=${workspaceId}&isPublished=${isPublished}&type=${jobType}`,
                '_blank'
              );
            } else if (type === JobNodeTypeEnum.TABLE_INTEGRATION_TASK) {
              window.open(
                `#${urls.offlineCollectDetail}?jobId=${id}&workspaceId=${workspaceId}&isPublished=${isPublished}&type=${jobType}`,
                '_blank'
              );
            }
            e.stopPropagation();
          }}
        />
      </div>
    );
  });

  // 根据 jobId 获取任务名称
  const dealJobIdToName = useMemoizedFn(async (id: string) => {
    if (!workspaceId || !id) {
      return;
    }
    const {result} = await getJobDetails(workspaceId, id);
    setJobName(nameIcon(result, id));
  });
  useEffect(() => {
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const taskParam: any = obj?.taskParam;
    if (!isEditing) {
      dealJobIdToName(taskParam?.integrationJobId);
    }
  }, [isEditing, selectedNodeId]);
  return (
    <>
      <Form.Item
        label="任务名称"
        name="integrationJobId"
        rules={isEditing ? [{required: true, message: '请选择任务'}] : []}
      >
        <EditableContent isEditing={isEditing} dealValue={() => jobName}>
          <RemoteSelect
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            optionFilterProp="label"
            objName="jobName"
            objId="jobId"
            queryList={getJobInfoList}
            params={[workspaceId, jobType]}
            placeholder="请选择任务"
            optionRender={(label, option) => {
              return nameIcon({...option.sourceObj, name: option.sourceObj.jobName}, option.sourceObj.jobId);
            }}
            // disabledOptionFn={(v) => {
            //   return !authCheck(v?.privileges, Privilege.Execute);
            // }}
          />
        </EditableContent>
      </Form.Item>
    </>
  );
};

export default IntegrationTaskParams;

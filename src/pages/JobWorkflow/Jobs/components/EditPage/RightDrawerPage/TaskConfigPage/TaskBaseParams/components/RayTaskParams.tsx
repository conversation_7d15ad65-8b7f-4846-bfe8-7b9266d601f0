import {getWorkspaceFileResult} from '@api/WorkArea';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import FilePathSelectWorkarea, {FileNodeTypeEnum} from '@components/FilePathSelectWorkarea';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {Form, Input, Table} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React, {useContext} from 'react';
import {useSelector} from 'react-redux';
import JobGlobalParamsFormItem from '../../../../../FormItem/JobGlobalParamsFormItem';

const RayTaskParams: React.FC<{form?: FormInstance}> = ({form}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {workspaceId} = useContext(WorkspaceContext);

  const envVars = Form.useWatch('envVars', form);

  const columns = [
    {
      title: '参数名',
      dataIndex: 'key',
      key: 'key',
      width: 70,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    },
    {
      title: '参数值',
      dataIndex: 'value',
      key: 'value',
      width: 100,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    }
  ];

  return (
    <>
      <Form.Item
        label="运行代码路径"
        name="codePath"
        rules={
          isEditing
            ? [
                {required: true, message: '请输入运行代码路径'},
                {
                  validator: (rule, value, callback) => {
                    getWorkspaceFileResult({path: value, workspaceId}).then((res) => {
                      if (!value) {
                        callback();
                        return;
                      }
                      if (res.result?.message) {
                        callback(res.result.message);
                      } else if (res.result?.nodeType !== FileNodeTypeEnum.FILE) {
                        callback('输入路径为文件，请重新选择');
                      } else {
                        callback();
                      }
                    });
                  }
                }
              ]
            : []
        }
      >
        <EditableContent isEditing={isEditing}>
          <FilePathSelectWorkarea selectNodeType={FileNodeTypeEnum.FILE} />
        </EditableContent>
      </Form.Item>
      <Form.Item
        label="入口命令"
        name="entryPoint"
        rules={[{required: isEditing ? true : false, message: '请输入入口命令'}]}
      >
        <EditableContent isEditing={isEditing}>
          <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
        </EditableContent>
      </Form.Item>
      <Form.Item label="环境变量" style={{marginBottom: 0}} name="envVars" />
      {isEditing ? (
        <JobGlobalParamsFormItem keyWidth="1 0 150px" formName="envVars" />
      ) : (
        <Table dataSource={envVars} columns={columns} pagination={false} />
      )}
    </>
  );
};

export default RayTaskParams;

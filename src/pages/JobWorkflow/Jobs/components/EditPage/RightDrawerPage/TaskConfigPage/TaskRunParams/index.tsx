import {EngineTypeEnum, JobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {Form} from 'acud';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../globalVar';

import useEnv from '@hooks/useEnv';
import {setFormIsDirty} from '@store/workflow';
import AihcTaskRunParams from './components/AihcTaskRunParams';
import ClusterList from './components/ClusterList';
import DataflowTaskRunParams from './components/DataflowTaskRunParams';
import SparkJarTaskRunParams from './components/SparkJarTaskRunParams';

// 任务运行参数
const TaskRunParams: React.FC = () => {
  // 选中的节点id
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const [form] = Form.useForm();
  const [detail, setDetail] = useState<IJsonNodeData | null>(null);
  const dispatch = useDispatch();

  // 修改表单 更新全局变量
  const changeForm = async (_: any, values: any) => {
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        ...values
      }
    };
    nodeMap.set(selectedNodeId, newObj);

    // 设置表单脏
    dispatch(setFormIsDirty(true));
  };

  // 初始化表单
  useEffect(() => {
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    setDetail(obj);
    form.setFieldsValue(obj?.taskParam);
    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, [selectedNodeId]);

  const {isPrivate} = useEnv();
  const nbEngineType = [EngineTypeEnum.RAY, !isPrivate && EngineTypeEnum.DORIS, EngineTypeEnum.SPARK].filter(
    Boolean
  );

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="basic"
        form={form}
        labelWidth={70}
        colon={false}
        onValuesChange={changeForm}
      >
        {detail?.type === JobNodeTypeEnum.DATAFLOW_TASK && <DataflowTaskRunParams />}
        {detail?.type === JobNodeTypeEnum.SPARK_JAR_TASK && <SparkJarTaskRunParams form={form} />}
        {detail?.type === JobNodeTypeEnum.PY_SPARK_TASK && <SparkJarTaskRunParams form={form} />}
        {detail?.type === JobNodeTypeEnum.RAY_TASK && <ClusterList engineType={[EngineTypeEnum.RAY]} />}
        {detail?.type === JobNodeTypeEnum.NOTEBOOK_TASK && <ClusterList engineType={nbEngineType} />}
        {detail?.type === JobNodeTypeEnum.AIHC_TASK && <AihcTaskRunParams form={form} />}
      </Form>
    </>
  );
};

export default TaskRunParams;

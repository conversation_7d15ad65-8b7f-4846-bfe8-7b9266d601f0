import {aihc<PERSON><PERSON>ues<PERSON>ist, aihcResourcepoolsList} from '@api/workflow';
import EditableContent from '@components/EditableContent';
import RemoteSelect from '@components/RemoteSelect';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {Form} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {useMemoizedFn} from 'ahooks';
import React, {useContext} from 'react';
import {useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../../globalVar';

// 任务运行参数
const AihcTaskRunParams: React.FC<{form?: FormInstance}> = ({form}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);

  // 修改任务参数表单
  const changeTaskParam = useMemoizedFn(async (key: string, value: any) => {
    form.setFieldsValue({
      [key]: value
    });
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        [key]: value
      }
    };
    nodeMap.set(selectedNodeId, newObj);
  });
  return (
    <>
      <Form.Item
        label="资源池"
        name="resourcePoolId"
        rules={isEditing ? [{required: true, message: '请选择资源池'}] : []}
      >
        <EditableContent isEditing={isEditing}>
          <RemoteSelect
            showSearch={true}
            optionFilterProp="label"
            objId="id"
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            queryList={aihcResourcepoolsList}
            onChangeSelect={() => {
              changeTaskParam('queue', undefined);
            }}
            params={[workspaceId]}
            placeholder="请选择"
          />
        </EditableContent>
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) => prevValues['resourcePoolId'] !== curValues['resourcePoolId']}
      >
        {({getFieldValue}) => {
          return (
            <Form.Item
              label="队列"
              name="queue"
              rules={isEditing ? [{required: true, message: '请选择队列'}] : []}
            >
              <EditableContent isEditing={isEditing}>
                <RemoteSelect
                  showSearch={true}
                  dropdownSearch={true}
                  optionFilterProp="label"
                  objId="name"
                  dropdownMatchSelectWidth={false}
                  showTitle={true}
                  dropdownStyle={{maxWidth: 300}}
                  queryList={aihcQueuesList}
                  params={[workspaceId, getFieldValue('resourcePoolId')]}
                  placeholder="请选择"
                />
              </EditableContent>
            </Form.Item>
          );
        }}
      </Form.Item>
    </>
  );
};

export default AihcTaskRunParams;

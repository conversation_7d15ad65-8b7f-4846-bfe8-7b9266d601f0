/**
 * 全局变量
 * nodeMap 记录节点数据 （主要用于 获取 节点数据）
 * jobData 记录任务数据 （主要用于提交获取工作流数据）
 * operatorDetailMap 记录算子数据(算子的动态表单和详情)
 * graph 记录 x6 图形对象（主要用于 获取 节点关系，位置信息）
 * <AUTHOR>
 */
import {Graph} from '@antv/x6';
import {IJob} from '@api/job';
import {IOperVersionOneRes} from '@api/metaRequest';
import {IJsonNodeData, IJsonOperatorData} from './EditContent/X6EditPage/type';

// 记录节点数据 结构与后端传参一致
export const nodeMap = new Map<string, IJsonNodeData | IJsonOperatorData>();

// 记录任务数据
export const jobData: {value: IJob} = {value: {}};

// 记录算子的参数配置 key 为  catalogName + schemaName + operatorName + version
export const operatorDetailMap = new Map<string, IOperVersionOneRes>();
// 记录自定义算子的参数配置 key 为  catalogName + schemaName + operatorName + version
export const customOperatorDetailMap = new Map<string, IOperVersionOneRes>();

// 记录 x6 图形对象
let graph: Graph | null = null;

export const setGraph = (g: Graph) => {
  graph = g;
};

export const getGraph = (): Graph | null => graph;

// 初始化任务数据
export const initJobData = () => {
  jobData.value = {
    name: '',
    code: '',
    description: '',
    isScheduled: false,
    globalParams: [],
    scheduleConf: {
      startTime: '',
      endTime: '',
      crontab: '',
      type: ''
    }
  };
};

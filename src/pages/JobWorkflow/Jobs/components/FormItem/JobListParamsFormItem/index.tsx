/**
 * 作业 全局参数 表单项
 * 提供列表页面 和 可视化 编辑界面 全局参数 ray 任务 表单项
 * 可以配置 表单名称
 * 默认最多 50 条
 *
 */
import IconSvg from '@components/IconSvg';
import {Button, Col, Form, Input, Row, Table} from 'acud';
import {OutlinedPlus} from 'acud-icon';
import React from 'react';
import styles from './index.module.less';
import EditableContent from '@components/EditableContent';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {FormInstance} from 'acud/lib/form';

// 参数名 最大长度
interface IJobListParams {
  disabled?: boolean;
  isEditing?: boolean;
  formName?: string;
  label?: string;
  maxLength?: number;
  valueMaxLength?: number;
  keyRule?: any[];
  form: FormInstance;
  // 重复校验
  sameValidator?: boolean;
}
// key value  参数配置
const JobListParamsFormItem: React.FC<IJobListParams> = ({
  disabled,
  isEditing = false,
  formName = 'params',
  label = 'params',
  maxLength = 50,
  valueMaxLength = 128,
  keyRule = [],
  sameValidator = true,
  form
}) => {
  const tableList = Form.useWatch(formName, form);
  if (!isEditing) {
    return (
      <Form.Item label={label} name={formName}>
        {tableList?.join(',') || '-'}
      </Form.Item>
    );
  }
  return (
    <>
      <Form.Item label={label} name={formName} style={{marginBottom: 0}} />
      <Form.List name={formName}>
        {(fieldsTem, {add, remove}) => {
          // 默认空的时候 也需要展示一条
          const fields = fieldsTem.length === 0 ? [{key: Date.now(), name: 0}] : fieldsTem;
          return (
            <>
              {fields.map(({key, name, ...restField}) => (
                <Row key={key} gutter={8} className={styles['global-params-form-item']}>
                  <Col flex={'1 1 100px'}>
                    <Form.Item
                      {...restField}
                      name={[name]}
                      initialValue={''}
                      dependencies={[...fields.map(({name}) => [formName, name])]}
                      rules={[
                        ...keyRule,
                        ({getFieldValue, getFieldsValue}) => ({
                          validator(_, value) {
                            // 只有 value 不为空 才必填
                            if (!value || !sameValidator) {
                              return Promise.resolve();
                            }

                            // 检查重复性
                            if (value) {
                              const allFields = getFieldsValue()[formName] || [];
                              const duplicateCount = allFields.filter(
                                (field: any, index: number) => field === value && index !== name
                              ).length;

                              if (duplicateCount > 0) {
                                return Promise.reject(new Error('参数名不能重复'));
                              }
                            }

                            return Promise.resolve();
                          }
                        })
                      ]}
                    >
                      <Input
                        className="w-full"
                        disabled={disabled}
                        allowClear
                        forbidIfLimit={true}
                        limitLength={valueMaxLength}
                        placeholder="请输入参数名"
                      />
                    </Form.Item>
                  </Col>
                  <Col flex={'0 0 16px'}>
                    {
                      <Button
                        className={styles['btn-delete']}
                        type="text"
                        disabled={fields?.length <= 1}
                        onClick={() => remove(name)}
                      >
                        <IconSvg size={16} type="delete" />
                      </Button>
                    }
                  </Col>
                </Row>
              ))}

              <Form.Item>
                <Button
                  className={styles['btn-add']}
                  disabled={disabled || fields.length >= maxLength}
                  onClick={() => add('')}
                  icon={<OutlinedPlus />}
                  type="actiontext"
                >
                  添加参数
                </Button>
              </Form.Item>
            </>
          );
        }}
      </Form.List>
    </>
  );
};

export default JobListParamsFormItem;

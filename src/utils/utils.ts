import {IQueryListParams, MenuItem} from '@type/common';
import cloneDeep from 'lodash/cloneDeep';
import {OrderType} from './enums';
import {AxiosResponse} from 'axios';
import {CascaderOption} from 'acud/lib/cascader/Cascader';
import {toast} from 'acud';
import {getEnv} from '@hooks/useEnv';

const {isPrivate} = getEnv();

// databuilder支持region和默认region
export const REGION_SETTINGS = {
  DEFAULT_REGION: 'bd',
  SUPPORT_REGION: ['bd', 'bj', 'cd'],
  WITH_EDAP_REGION: ['bd', 'bj']
};

/**
 * 给数字增加千位分割符
 */
export function addThousandSeparator(num: number | string) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/** 生成随机报表的名称 */
export function genRandomReportName() {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return `ar-${result}`;
}

/** 根据文件路径获取文件名称 */
export function getFileNameFromPath(filePath: string) {
  const pathArray = filePath.split('/');
  const fileName = pathArray[pathArray.length - 1];
  return fileName;
}

// 默认排序参数
export const DefaultPage = {pageNo: 1, pageSize: 10};
/** 处理表格查询参数
 * 1. 处理没有分页情况
 * 2. 处理排序， 删除sorter
 * 3. 处理查询关键字 如果keywordType为空 则删除keywordType
 * 4. 处理过滤条件 如果filters不为空 处理筛选条件 并删除filters
 * 5. 处理post请求 如果isPost为true 则filters 为数组
 */
export function dealQueryParams(params?: IQueryListParams, isPost = false) {
  if (!params) {
    return DefaultPage;
  }
  const cloneParams = cloneDeep(params);

  // 处理没有分页情况
  if (!cloneParams.pageNo) {
    cloneParams.pageNo = DefaultPage.pageNo;
  }
  if (!cloneParams.pageSize) {
    cloneParams.pageSize = DefaultPage.pageSize;
  }

  // 处理排序
  if (cloneParams.sorter && cloneParams.sorter.order) {
    cloneParams.order = cloneParams.sorter.order?.includes(OrderType.asc) ? OrderType.asc : OrderType.desc;
    cloneParams.orderBy = cloneParams.sorter.columnKey;
  } else {
    delete cloneParams.order;
    delete cloneParams.orderBy;
  }
  delete cloneParams.sorter;

  // 查询关键字
  if (!cloneParams.keyword) {
    delete cloneParams.keyword;
    delete cloneParams.keywordType;
  }
  // 处理过滤条件
  if (cloneParams.filters) {
    const obj = cloneParams.filters;
    Object.keys(obj)
      // 可能存在 undefined 的情况 需要排查
      .filter((key) => obj[key] !== undefined && obj[key] !== null)
      .forEach((key) => {
        if (!isPost && Array.isArray(obj[key])) {
          cloneParams[key] = obj[key]?.join(',');
        } else {
          cloneParams[key] = obj[key];
        }
      });
    delete cloneParams.filters;
  }

  return cloneParams;
}

/** 文件类型 */
const MIME_TYPE = {
  json: 'application/json',
  txt: 'text/plain',
  html: 'text/html',
  css: 'text/css',
  js: 'text/javascript',
  xml: 'application/xml'
};
/** 下载文本文件 */
export const downloadStr = (data: string, filename = 'data.json') => {
  const type = MIME_TYPE[filename.split('.').pop() as keyof typeof MIME_TYPE];
  const blob = new Blob([data], {type}); // 创建 Blob
  const url = URL.createObjectURL(blob); // 创建 URL
  const a = document.createElement('a'); // 创建 <a> 标签
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click(); // 触发下载
  document.body.removeChild(a);
  URL.revokeObjectURL(url); // 释放 URL
};

/**
 * 转换显示文件内容大小展示的的方法
 * 参考：https://github.com/vpodk/bytes-formatter/blob/master/index.js
 * @param size 文件大小
 * @returns 带单位 & 合理显示文件大小
 */
const FORMATS = ['bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
export function formatBytes(size: number | string) {
  if (typeof size !== 'number') {
    size = Number(size);
  }

  if (isNaN(size) || size < 0) {
    console.error('[formatBytes]方法 Invalid input: 期望是一个有效的正数');
    return '-';
  }

  let i = 0;

  while (1023 < size) {
    size /= 1024;
    ++i;
  }

  return (i ? size.toFixed(2) : size) + ' ' + FORMATS[i];
}
/**
 * 处理秒数 转换为 日时分秒
 * @param seconds 秒数
 * @returns 日时分秒
 */
export function formatSeconds(seconds?: number) {
  if (typeof seconds !== 'number' || seconds < 0) {
    return '-';
  }
  if (seconds === 0) {
    return '0s';
  }
  const strType = ['d', 'h', 'min', 's'];
  let result = '';
  const days = Math.floor(seconds / (24 * 60 * 60));
  if (days > 0) {
    result += days + strType[0];
  }
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  if (hours > 0) {
    result += hours + strType[1];
  }
  const minutes = Math.floor((seconds % (60 * 60)) / 60);
  const sec = seconds % 60;
  if (minutes > 0) {
    result += minutes + strType[2];
  }
  if (sec > 0) {
    result += sec + strType[3];
  }
  return result;
}

/** 递归处理菜单 */
export function recursiveMenus(menus: MenuItem[]): MenuItem[] {
  return menus.reduce((result: MenuItem[], item: MenuItem) => {
    // 如果是分组，就直接打平children 跳过分组节点 分组节点只提供名称
    if (item.isMenuGroup) {
      const processedChildren = recursiveMenus(item.children);
      result.push(...processedChildren);
      return result;
    }

    const {children, ...rest} = item;
    result.push({...rest});

    if (children && children.length) {
      const processedChildren = recursiveMenus(children);
      result.push(...processedChildren);
    }

    return result;
  }, []);
}

// 验证文件路径函数
export function validatePath(path: string) {
  // 获取字符串的字节长度
  function getByteLength(str: string) {
    return new TextEncoder().encode(str).length;
  }

  // 文件路径正则表达式
  // 不能以/或者\字符开头，不能出现连续的//，不能仅为.
  const pathFormatRegex = /^(?![/\\])(?!.*\/\/)(?!^\.$)[\s\S]+$/;

  if (typeof path !== 'string') {
    return false;
  }

  // 检查字节长度
  const byteLength = getByteLength(path);
  if (byteLength < 1 || byteLength > 254) {
    return false;
  }

  // 私有化环境，文件名不能包含冒号和空格
  if (isPrivate && validateFileNameInvalid(path)) {
    return false;
  }

  // 检查格式要求
  return pathFormatRegex.test(path);
}

// 校验文件是否存在后缀
export function validateFileExtend(fileName: string) {
  const regex = /\.([^.]+\.)*[^.]+$/;
  return regex.test(fileName);
}

// 校验文件名称，是否存在冒号和空格
export function validateFileNameInvalid(fileName: string) {
  const regex = /[\s:：]/;
  return regex.test(fileName);
}

/**
 * Cascader 根据 value path 获取对应的 label path
 * @param valuePath - Cascader 返回的 value 数组，如 ['ray', 'v1']
 * @param options - 原始的 Cascader options
 * @returns label 数组，如 ['AI 增强版', '1.0']
 */
export const getCascaderLabelByValuePath = (valuePath: string[], options: CascaderOption[]): string[] => {
  const labelPath: string[] = [];
  let currentOptions: CascaderOption[] | undefined = options;

  for (const value of valuePath) {
    if (!currentOptions) break;
    const matchedOption = currentOptions.find((opt) => opt.value === value);
    if (!matchedOption) break;
    labelPath.push(matchedOption.labelText as string);
    currentOptions = matchedOption.children;
  }

  return labelPath;
};

export const reducePathLevel = (path: string): string => {
  // 如果路径以 '/' 结尾，先去掉末尾的 '/'
  if (path.endsWith('/')) {
    path = path.slice(0, -1);
  }
  // 找到最后一个 '/' 的位置
  const lastSlashIndex = path.lastIndexOf('/');
  // 如果存在 '/'，则截取到最后一个 '/' 之前的部分
  if (lastSlashIndex !== -1) {
    return path.slice(0, lastSlashIndex + 1);
  }
  return '';
};
/**
 * 判断文件列表是否符合上传要求
 * 1. 文件数量不超过100
 * 2. 单个文件大小不超过100MB
 * 3. 文件后缀不为空
 * 4. 总文件大小不超过500MB
 * @param fileList 文件列表
 * @returns boolean
 */
export function isFilelistReasonble(fileList: any[]) {
  if (!fileList.length) {
    return false;
  }
  if (fileList.length > 100) {
    toast.error({
      message: '上传文件数量不能超过100个',
      duration: 5
    });
    return false;
  }
  const totalSize = fileList.reduce((sum, item) => sum + item.size, 0);
  const isLt500M = totalSize <= 1024 * 1024 * 500;
  if (!isLt500M) {
    toast.error({
      message: '单次上传文件总大小不能超过500MB',
      duration: 5
    });
    return false;
  }

  fileList.forEach((file: any) => {
    if (!validateFileExtend(file.name)) {
      // 校验文件是否存在后缀
      toast.error({
        message: `不支持上传缺失后缀的文件`,
        duration: 5
      });
      return false;
    }
    const isLt100M = file.size <= 1024 * 1024 * 100;

    if (!isLt100M) {
      toast.error({
        message: '单个文件要小于100MB',
        duration: 5
      });
      return false;
    }
  });
  return true;
}

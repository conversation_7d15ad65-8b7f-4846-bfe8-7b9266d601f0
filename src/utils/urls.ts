// 工作空间下的路由前缀
export const WORKSPACE_ROUTER_PREFIX = '/workspace';

const urls = {
  /** 开通页 */
  activation: '/activation',
  /** 多模态-工作空间管理 */
  manageWorkspace: '/manage-workspace',
  /** 结构化-工作空间管理 */
  manageEDAPWorkspace: '/edap-workspace',
  /** 多模态-工作空间管理 - 详情 */
  manageWorkspaceDetail: '/manage-workspace/detail',
  /** 元存储 */
  metastore: '/metastore',
  /** MetaData */
  metaData: '/workspace/meta/data',
  /** 算子版本配置 */
  operatorVersionConfig: `${WORKSPACE_ROUTER_PREFIX}/meta/operator/version/config`,
  /** 数据源 */
  connection: `${WORKSPACE_ROUTER_PREFIX}/connection`,
  /** 数据源 */
  connectionDetail: `${WORKSPACE_ROUTER_PREFIX}/connection/detail`,
  /** 工作区 */
  workArea: `${WORKSPACE_ROUTER_PREFIX}/workarea`,
  /** notebook */
  notebook: `${WORKSPACE_ROUTER_PREFIX}/workarea/notebook`,
  /** 计算资源 */
  compute: `${WORKSPACE_ROUTER_PREFIX}/compute`,
  /** 计算资源 - 常驻资源创建 */
  computeCreateBasedInstance: `${WORKSPACE_ROUTER_PREFIX}/compute/create-based`,
  /** 计算资源 - 分析与AI搜索实例创建 */
  computeCreateAnalysisInstance: `${WORKSPACE_ROUTER_PREFIX}/compute/create-analysis`,
  /** 计算资源 - 任务实例模版创建 */
  computeCreateTaskInstance: `${WORKSPACE_ROUTER_PREFIX}/compute/create-task`,
  /** 计算资源 - 任务实例详情 */
  computeTaskInstanceDetail: `${WORKSPACE_ROUTER_PREFIX}/compute/task-detail`,
  /** 计算资源 - 资源池创建 */
  computeCreatePool: `${WORKSPACE_ROUTER_PREFIX}/compute/create-pool`,
  /** 计算资源 - 资源池详情 */
  computePoolDetail: `${WORKSPACE_ROUTER_PREFIX}/compute/pool-detail`,
  /** 计算资源创建 */
  computeCreate: `${WORKSPACE_ROUTER_PREFIX}/compute/create`,
  /** 计算资源 - 源连接与集成实例创建 */
  computeCreateConnAndIntegInstance: `${WORKSPACE_ROUTER_PREFIX}/compute/create-connection-and-integration`,
  /** 工作流 */
  job: `${WORKSPACE_ROUTER_PREFIX}/job`,
  /** 工作流详情页 */
  jobDetail: `${WORKSPACE_ROUTER_PREFIX}/job/detail`,
  /** 运行记录 */
  jobInstance: `${WORKSPACE_ROUTER_PREFIX}/job/instance`,
  /** 模板 */
  templateDetail: `${WORKSPACE_ROUTER_PREFIX}/template/detail`,
  /** 运行记录结果 */
  jobResult: `${WORKSPACE_ROUTER_PREFIX}/job/result`,
  /** 运行记录结果 实例高亮 */
  jobInstanceResult: `${WORKSPACE_ROUTER_PREFIX}/job/instance/result`,
  /** 数据接入 */
  dataIngestion: `${WORKSPACE_ROUTER_PREFIX}/data-ingestion`,
  /** 数据集成 */
  integration: `${WORKSPACE_ROUTER_PREFIX}/integration`,
  /** 数据集成 - 文件采集 - 详情 */
  fileCollectDetail: `${WORKSPACE_ROUTER_PREFIX}/integration/file-collect-detail`,
  /** 数据集成 - 文件采集 - 创建 */
  fileCollectCreate: `${WORKSPACE_ROUTER_PREFIX}/integration/file-collect-create`,
  /** 数据集成 - 离线采集 */
  offlineCollectDetail: `${WORKSPACE_ROUTER_PREFIX}/integration/offline-detail`,
  /** 数据集成 - 离线采集 - 配置 */
  offlineCollectConfig: `${WORKSPACE_ROUTER_PREFIX}/integration/offline-config`,
  /** 数据集成 - 离线采集 - 结果 */
  offlineCollectResult: `${WORKSPACE_ROUTER_PREFIX}/integration/offline-result`,
  /** 地域无效遮挡页 */
  regionInvalid: `/region-invalid`
};

export default urls;

/**
 * @file 正则校验规则
 * <AUTHOR>
 */

export const RULE = {
  // 中⽂、英⽂、数字、中划线、下划线，⻓度为1～50个字符
  specialName50: /^(?![-_])[\u4e00-\u9fa5a-zA-Z0-9-_]{0,50}$/,
  specialName50Text: '支持中文、英文、数字、中划线、下划线，长度为1～50个字符，不能以中划线、下划线开头',

  // 中⽂、英⽂、数字、中划线、下划线，⻓度为
  workflowName: /^[\u4e00-\u9fa5a-zA-Z0-9-_]{0,256}$/,
  workflowNameText: '支持中文、英文、数字、中划线、下划线，不超过256字符',

  // 必须以字母开头，只能包含字母、下划线和数字
  workflowGlobalParamsKey: /^[a-zA-Z][a-zA-Z0-9_]{0,64}$/,
  workflowGlobalParamsKeyText: '必须以字母开头，只能包含字母、下划线和数字',

  // 必须以字母开头，只能包含字母、下划线 点和数字
  workflowSparkJarParamsKey: /^[a-zA-Z][a-zA-Z0-9_.]{0,64}$/,
  workflowSparkJarKeyText: '必须以字母开头，只能包含字母、下划线、点和数字',

  // 英文开头, 支持英文、数字、下划线，不超过128个字符
  workflowAihcJobNameRule: /^[a-z][a-z0-9_]{0,127}[a-z0-9]$/,
  workflowAihcJobNameRuleText: '必须小写英文开头,小写字母或数字结尾，支持小写英文、数字、下划线',

  // 英文开头, 支持英文、数字、下划线，不超过128个字符
  sinkNameRule: /^[a-zA-Z][a-zA-Z0-9_]{0,127}$/,
  sinkNameRuleText: '支持英文开头, 支持英文、数字、下划线，不超过128个字符',

  // 支持英文、数字、下划线，不超过128个字符
  tableSuffixNameRule: /^[a-zA-Z_][a-zA-Z0-9_]{0,127}$/,
  tableSuffixNameRuleText: '支持字母、下划线开头，支持英文、数字、下划线，不超过128个字符',

  // 英文、数字、下划线，长度为1～64个字符
  specialName64: /^[a-zA-Z0-9_]{0,64}$/,
  specialName64Text: '支持英文、数字、下划线，长度为1～64个字符',

  // 英文、数字、下划线，以字母开头，长度为1～64个字符
  specialNameStartEn64: /^[a-zA-Z][a-zA-Z0-9_]{0,63}$/,
  specialNameStartEn64Text: '支持英文、数字、下划线，必须以字母开头，长度为1～64个字符',

  // eslint-disable-next-line no-control-regex
  bos: /^([a-z0-9][a-z0-9-]{2,61}[a-z0-9])(\/((?!.*\/\/)(?!.*\/$)(?!.*\\$)(?!.*\.\.\/)[A-Za-z0-9-_=:\\/.]{1,254}))*$/,
  bosText: 'BOS路径格式不准确，请参考文档（https://cloud.baidu.com/doc/BOS/s/Fk4xtwbze）',

  // 仅支持中文、英文字母、数字、下划线、中划线，长度不超过32字符
  connectionName: /^[\u4e00-\u9fa5a-zA-Z0-9-_]{0,32}$/,
  connectionNameText: '仅支持中文、英文字母、数字、下划线、中划线，长度不超过32字符',

  // 仅支持数字，长度不超过32字符
  connectionPort: /^[0-9]{0,32}$/,
  connectionPortText: '仅支持数字，长度不超过32字符',

  // hdfs路径
  hdfs: /^(?![\\/])(?!.*\/\/)([a-zA-Z0-9_-]{1,253})(\/[a-zA-Z0-9_-]{1,253})*$/,
  hdfsWithStart: /^hdfs:\/\/(?![\\/])(?!.*\/\/)([a-zA-Z0-9_-]{1,253})(\/[a-zA-Z0-9_-]{1,253})*$/,
  // 支持端口号的hdfs路径
  hdfsDefaultFs: /^(?![\\/])(?!.*\/\/)([a-zA-Z0-9._-]{1,253})(:\d+)?(\/[a-zA-Z0-9_-]{1,253})*$/,
  hdfsText: 'HDFS路径格式不准确'
};

import {Button, Menu, Tooltip} from 'acud';
import {MenuItemProps} from 'acud/lib/menu/MenuItem';
import React, {memo} from 'react';
import {TooltipConfig, TooltipType} from './constants';

interface AuthMenuItemProps extends MenuItemProps {
  // 是否有授权
  isAuth: boolean;
  isDisabled?: boolean;
  tooltipType?: TooltipType;
}

/**
 * 包含鉴权和 tooltip 提示菜单item，与普通 MenuItem 用法参数一致，需要传入鉴权参数
 */
const AuthMenuItem: React.FC<AuthMenuItemProps> = ({
  isAuth,
  tooltipType = TooltipType.Resource,
  children,
  ...props
}) => {
  return (
    <>
      {isAuth ? (
        <Menu.Item {...props}>{children}</Menu.Item>
      ) : (
        <Menu.Item {...props} disabled={true}>
          <Tooltip title={TooltipConfig[tooltipType]}>{children}</Tooltip>
        </Menu.Item>
      )}
    </>
  );
};

export default memo(AuthMenuItem);

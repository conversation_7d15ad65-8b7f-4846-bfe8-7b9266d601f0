import {Ellipsis} from '@baidu/bce-react-toolkit';
import {useDebounceFn, useMemoizedFn} from 'ahooks';
import React, {cloneElement, useEffect, useState} from 'react';

interface EditableContentProps {
  isEditing?: boolean;
  showEllipsis?: boolean;
  children?: any; // 编辑态输入组件
  value?: any;
  dealValue?: (value: any) => any;
  getDetail?: (value?: any) => Promise<any>;
  onChange?: (value: any) => void;
}
// 可切换只读的输入框
// 注意 这个组件会覆盖onChange事件和 value 赋值

const EditableContent: React.FC<EditableContentProps> = ({
  isEditing,
  showEllipsis = true,
  children,
  value,
  onChange,
  dealValue,
  getDetail
}) => {
  const [detail, setDetail] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  // 克隆 children，注入 value 和 onChange
  const controlledChild = cloneElement(children, {
    value,
    onChange
  });

  const initFn = useMemoizedFn(() => {
    if (!value && value !== 0) {
      setDetail('-');
    } else if (dealValue) {
      setDetail(dealValue(value));
    } else if (getDetail) {
      if (!value) {
        setDetail('-');
        return;
      }

      setLoading(true);
      getDetail(value).then((res) => {
        setDetail(res);
        setLoading(false);
      });
    } else {
      setDetail(value);
    }
  });
  // TODO： 添加100ms 延迟 避免多次查询详情接口
  const {run} = useDebounceFn(
    () => {
      initFn();
    },
    {
      wait: 100
    }
  );

  useEffect(() => {
    run();
  }, [value, dealValue, getDetail, run]);

  return (
    <>
      {isEditing && controlledChild}
      {!isEditing && showEllipsis && <Ellipsis tooltip={detail}>{detail}</Ellipsis>}
      {!isEditing && !showEllipsis && detail}
    </>
  );
};

export default React.memo(EditableContent);

import {Privilege, ResourceType} from '@api/permission/type';
import urls from '@utils/urls';

export const PrivilegeInfo = {
  [Privilege.All]: {
    name: '全部权限',
    privilege: Privilege.All
  },
  [Privilege.Manage]: {
    name: '管理',
    privilege: Privilege.Manage
  },
  [Privilege.Browse]: {
    name: '查看元数据',
    privilege: Privilege.Browse,
    description: '授予读取对象元数据的权限'
  },
  [Privilege.ReadVolume]: {
    name: '读卷',
    privilege: Privilege.ReadVolume,
    description: '授予从卷中读取数据的权限'
  },
  [Privilege.ReadTable]: {
    name: '读表',
    privilege: Privilege.ReadTable,
    description: '授予从表读取数据的权限'
  },
  [Privilege.ReadDataset]: {
    name: '读数据集',
    privilege: Privilege.ReadDataset,
    description: '授予从数据集读取数据的权限'
  },
  [Privilege.Execute]: {
    name: '执行',
    privilege: Privilege.Execute,
    description: '授予执行算子或模型的权限'
  },
  [Privilege.WriteVolume]: {
    name: '写卷',
    privilege: Privilege.WriteVolume,
    description: '授予向卷中写数据的权限'
  },
  [Privilege.WriteTable]: {
    name: '修改表数据',
    privilege: Privilege.WriteTable,
    description: '授予向表中增加、删除和修改数据的权限'
  },
  [Privilege.WriteDataset]: {
    name: '写数据集',
    privilege: Privilege.WriteDataset,
    description: '授予向数据集中写数据的权限'
  },
  [Privilege.CreateSchema]: {
    name: '创建模式',
    privilege: Privilege.CreateSchema,
    description: '授予在目录中创建模式的权限'
  },
  [Privilege.CreateVolume]: {
    name: '创建卷',
    privilege: Privilege.CreateVolume,
    description: '授予创建卷的权限'
  },
  [Privilege.CreateTable]: {
    name: '创建表',
    privilege: Privilege.CreateTable,
    description: '授予创建表的权限'
  },
  [Privilege.CreateDataset]: {
    name: '创建数据集',
    privilege: Privilege.CreateDataset,
    description: '授予创建数据集的权限'
  },
  [Privilege.CreateDatasetVersion]: {
    name: '创建数据集新版本',
    privilege: Privilege.CreateDatasetVersion,
    description: '授予在现有数据集上创建版本的权限'
  },
  [Privilege.CreateModel]: {
    name: '创建模型',
    privilege: Privilege.CreateModel,
    description: '授予创建模型的权限'
  },
  [Privilege.CreateModelVersion]: {
    name: '创建模型新版本',
    privilege: Privilege.CreateModelVersion,
    description: '授予在现有模型上创建版本的权限'
  },
  [Privilege.CreateOperator]: {
    name: '创建算子',
    privilege: Privilege.CreateOperator,
    description: '授予新建算子的权限'
  },
  [Privilege.CreateOperatorVersion]: {
    name: '创建算子新版本',
    privilege: Privilege.CreateOperatorVersion,
    description: '授予在现有算子上创建版本的权限'
  },
  [Privilege.UseConnection]: {
    name: '使用',
    privilege: Privilege.UseConnection,
    description: '授予使用连接的能力'
  },
  [Privilege.CreateCatalog]: {
    name: '创建目录',
    privilege: Privilege.CreateCatalog,
    description: '授予创建数据目录的权限'
  },
  [Privilege.CreateConnection]: {
    name: '创建数据源',
    privilege: Privilege.CreateConnection,
    description: '授予创建数据源的权限'
  }
};

// 数据源
export const ConnectionConfig = [PrivilegeInfo[Privilege.UseConnection]];

// 元数据目录
export const CatalogConfig = [
  {
    groupName: '元数据',
    privilege: [PrivilegeInfo[Privilege.Browse]]
  },
  {
    groupName: '读取',
    privilege: [
      PrivilegeInfo[Privilege.ReadVolume],
      PrivilegeInfo[Privilege.ReadTable],
      PrivilegeInfo[Privilege.ReadDataset],
      PrivilegeInfo[Privilege.Execute]
    ]
  },
  {
    groupName: '编辑',
    privilege: [
      PrivilegeInfo[Privilege.WriteVolume],
      PrivilegeInfo[Privilege.WriteTable],
      PrivilegeInfo[Privilege.WriteDataset]
    ]
  },
  {
    groupName: '创建',
    privilege: [
      PrivilegeInfo[Privilege.CreateSchema],
      PrivilegeInfo[Privilege.CreateVolume],
      PrivilegeInfo[Privilege.CreateTable],
      PrivilegeInfo[Privilege.CreateDataset],
      PrivilegeInfo[Privilege.CreateDatasetVersion],
      PrivilegeInfo[Privilege.CreateModel],
      PrivilegeInfo[Privilege.CreateModelVersion],
      PrivilegeInfo[Privilege.CreateOperator]
    ]
  }
];

// doris 元数据目录
export const DorisCatalogConfig = [
  {
    groupName: '元数据',
    privilege: [PrivilegeInfo[Privilege.Browse]]
  },
  {
    groupName: '读取',
    privilege: [PrivilegeInfo[Privilege.ReadTable]]
  },
  {
    groupName: '编辑',
    privilege: [PrivilegeInfo[Privilege.WriteTable]]
  },
  {
    groupName: '创建',
    privilege: [PrivilegeInfo[Privilege.CreateSchema], PrivilegeInfo[Privilege.CreateTable]]
  }
];

// 元数据模式
export const SchemaConfig = [
  {
    groupName: '读取',
    privilege: [
      PrivilegeInfo[Privilege.ReadVolume],
      PrivilegeInfo[Privilege.ReadTable],
      PrivilegeInfo[Privilege.ReadDataset],
      PrivilegeInfo[Privilege.Execute]
    ]
  },
  {
    groupName: '编辑',
    privilege: [
      PrivilegeInfo[Privilege.WriteVolume],
      PrivilegeInfo[Privilege.WriteTable],
      PrivilegeInfo[Privilege.WriteDataset]
    ]
  },
  {
    groupName: '创建',
    privilege: [
      PrivilegeInfo[Privilege.CreateVolume],
      PrivilegeInfo[Privilege.CreateTable],
      PrivilegeInfo[Privilege.CreateDataset],
      PrivilegeInfo[Privilege.CreateDatasetVersion],
      PrivilegeInfo[Privilege.CreateModel],
      PrivilegeInfo[Privilege.CreateModelVersion],
      PrivilegeInfo[Privilege.CreateOperator]
    ]
  }
];

// 元数据模式
export const DorisSchemaConfig = [
  {
    groupName: '读取',
    privilege: [PrivilegeInfo[Privilege.ReadTable]]
  },
  {
    groupName: '编辑',
    privilege: [PrivilegeInfo[Privilege.WriteTable]]
  },
  {
    groupName: '创建',
    privilege: [PrivilegeInfo[Privilege.CreateTable]]
  }
];

// 元数据表
export const TableConfig = [PrivilegeInfo[Privilege.ReadTable], PrivilegeInfo[Privilege.WriteTable]];

// 元数据卷
export const VolumeConfig = [PrivilegeInfo[Privilege.ReadVolume], PrivilegeInfo[Privilege.WriteVolume]];

// 元数据数据集
export const DatasetConfig = [
  PrivilegeInfo[Privilege.ReadDataset],
  PrivilegeInfo[Privilege.WriteDataset],
  PrivilegeInfo[Privilege.CreateDatasetVersion]
];

// 元数据模型
export const ModelConfig = [PrivilegeInfo[Privilege.Execute], PrivilegeInfo[Privilege.CreateModelVersion]];

// 元数据算子
export const OperatorConfig = [
  PrivilegeInfo[Privilege.Execute],
  PrivilegeInfo[Privilege.CreateOperatorVersion],
  PrivilegeInfo[Privilege.Manage]
];

// 元存储
export const MetastoreConfig = [
  {
    groupName: '创建',
    privilege: [PrivilegeInfo[Privilege.CreateCatalog], PrivilegeInfo[Privilege.CreateConnection]]
  }
];

export const ResourceConfig = {
  [ResourceType.Metastore]: {
    name: '元存储',
    config: MetastoreConfig
  },
  [ResourceType.Catalog]: {
    name: '数据目录',
    config: CatalogConfig
  },
  [ResourceType.Schema]: {
    name: '数据模式',
    config: SchemaConfig
  },
  [ResourceType.Table]: {
    name: '数据表',
    config: TableConfig
  },
  [ResourceType.Volume]: {
    name: '数据卷',
    config: VolumeConfig
  },
  [ResourceType.Dataset]: {
    name: '数据集',
    config: DatasetConfig
  },
  [ResourceType.Model]: {
    name: '模型',
    config: ModelConfig
  },
  [ResourceType.Operator]: {
    name: '算子',
    config: OperatorConfig
  },
  [ResourceType.Connection]: {
    name: '数据源',
    config: ConnectionConfig
  }
};

export const getMetaUrl = (fullName, workspaceId) => {
  const pathArr = fullName.split('.');
  const catalog = pathArr?.[0] ? `&catalog=${pathArr?.[0]}` : '';
  const schema = pathArr?.[1] ? `&schema=${pathArr?.[1]}` : '';
  const node = pathArr?.[2] ? `&node=${pathArr?.[2]}` : '';
  return `${urls.metaData}?workspaceId=${workspaceId}${catalog}${schema}${node}`;
};

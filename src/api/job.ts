import {
  CronTypeEnum,
  JobExecutionTypeEnum,
  JobFailureStrategyEnum,
  JobPriorityEnum,
  JobScheduleStatusEnum,
  TaskAlertSceneEnum,
  TaskAlertTypeEnum,
  WorkflowPageCreateTypeEnum
} from '@pages/JobWorkflow/constants';
import {IQueryListParams} from '@type/common';
import {BaseResponseType, request, urlPrefix} from './apiFunction';

import {dealQueryParams} from '@utils/utils';
import moment from 'moment';
import {WorkspaceType} from './workflow/private';

// 工作流
export interface IJob {
  workspaceId?: string;
  jobId?: string;
  name?: string;
  code?: string;
  alertConf?: string;
  // 前端使用的字段 告警配置  放置在 alertConf json 字符串
  jobAlertStrategyList?: IAlertStrategy[];
  description?: string;
  isScheduled?: boolean; // 是否调度 废弃，使用scheduleStatus
  scheduleStatus?: JobScheduleStatusEnum;
  globalParams?: IKeyValue[];
  scheduleConf?: ICron;
  createdAt?: string;
  createUser?: string;
  updatedAt?: string;
  updateUser?: string;
  privileges?: string[]; // 权限

  priority?: JobPriorityEnum; // 优先级
  executionType?: JobExecutionTypeEnum; // 执行策略
  maxConcurrency?: number; // 最大并发
  failureStrategy?: JobFailureStrategyEnum; // 失败策略
  // 工作区id
  linkFileId?: string;
  // 父目录id
  parentDirId?: string;
}

/** 全局参数 */
export interface IKeyValue {
  key: string;
  value: string;
}

/** cron 类型 */
export interface ICron {
  startTime: string;
  endTime: string;
  crontab: string;
  type: string;
  scheduleStatus?: JobScheduleStatusEnum;
}

// cron 表单
export interface ICronForm {
  startTime?: moment.Moment;
  endTime?: moment.Moment;
  type?: CronTypeEnum;
  crontab?: string;
  minute?: number;
  time?: moment.Moment;
  day?: number[];
  month?: number[];
  week?: number[];
}

// 告警策略
export interface IAlertStrategy {
  alertUser: string;
  alertType: TaskAlertTypeEnum;
  alertAddress: string;
  alertScenes: TaskAlertSceneEnum[];
}

export interface IQueryJobListParams extends IQueryListParams {
  workspaceId?: string;
}

/** 查询列表 */
export function queryJobList(
  sourceParams: IQueryJobListParams,
  workspaceId
): BaseResponseType<{
  jobs: Array<IJob>;
  total: number;
}> {
  const params = dealQueryParams(sourceParams, true);
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/list`,
    method: 'POST',
    data: params
  });
}

/** 创建 */
export function createJob(
  params: IJob,
  workspaceId
): BaseResponseType<{
  jobId: string;
}> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs`,
    method: 'POST',
    data: {...params, workspaceId}
  });
}
/** 修改 */
export function editJob(params: IJob): BaseResponseType<{
  jobId: number;
}> {
  // 告警策略 如果微空值 则设置为 undefined
  let alertConf = JSON.stringify({
    jobAlertStrategyList: params.jobAlertStrategyList || []
  });
  if (!params.jobAlertStrategyList || params.jobAlertStrategyList.length === 0) {
    alertConf = undefined;
  }
  const data = {
    name: params?.name,
    description: params?.description,
    code: params?.code,
    scheduleStatus: params?.scheduleStatus,
    scheduleConf: params?.scheduleConf,
    globalParams: params?.globalParams,
    // 告警
    alertConf,
    // 优先级 失败策略
    priority: params?.priority,
    executionType: params?.executionType,
    maxConcurrency: params?.maxConcurrency,
    failureStrategy: params?.failureStrategy
  };

  return request({
    url: `${urlPrefix}/workspaces/${params.workspaceId}/jobs/${params.jobId}`,
    method: 'PATCH',
    data
  });
}

/** 详情 */
export function detailJob(
  workspaceId?: string,
  jobId?: string,
  workspaceType?: WorkspaceType
): BaseResponseType<IJob> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}`,
    method: 'GET',
    params: {
      workspaceType: workspaceType || WorkspaceType.MULTIMODAL
    }
  }).then((res: any) => {
    if (res.success) {
      try {
        // 默认 格式化
        res.result.code = JSON.stringify(JSON.parse(res.result.code), null, 4);
        // 处理告警信息
        res.result.jobAlertStrategyList = JSON.parse(res.result?.alertConf || '{}').jobAlertStrategyList;
      } catch (error) {
        console.log(error);
      }
    }
    return res;
  });
}

/** 详情 快速接口 没有冗余字段 */
export function detailJobFast(workspaceId?: string, jobId?: string): BaseResponseType<IJob> {
  return request({
    // 等待后端提供新接口
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}`,
    method: 'GET'
  });
}

/** 复制 */
export function copyJob(job: IJob, type: WorkflowPageCreateTypeEnum): BaseResponseType<{jobId: string}> {
  // 复制调用单独接口 使用管理权限
  if (type === WorkflowPageCreateTypeEnum.COPY) {
    return request({
      url: `${urlPrefix}/workspaces/${job.workspaceId}/jobs/${job.jobId}/copy`,
      method: 'POST'
    });
  }

  // 发起请求
  return createJob(
    {
      name: renameJobName({name: job.name, type}),
      description: job.description,
      code: job.code
    },
    job.workspaceId
  );
}

/** 运行 DRAFT_DEBUG（草稿调试）、TASK_RUN（任务运行）*/
export enum JobRunType {
  DRAFT_DEBUG = 'DRAFT_DEBUG',
  TASK_RUN = 'TASK_RUN'
}

// SINGLE（单次执行）、SCHEDULE（例行执行）
export enum JobRunTriggerTypeEnum {
  SINGLE = 'SINGLE',
  SCHEDULE = 'SCHEDULE'
}
export const JobRunTriggerTypeChineseEnum = {
  [JobRunTriggerTypeEnum.SINGLE]: '单次执行',
  [JobRunTriggerTypeEnum.SCHEDULE]: '例行执行'
};
export function startJob(
  workspaceId?: string,
  jobId?: string,
  globalParams?: IKeyValue[],
  priority?: JobPriorityEnum
): BaseResponseType<{id: string}> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}/start`,
    method: 'POST',
    data: {
      runType: JobRunType.TASK_RUN,
      triggerType: JobRunTriggerTypeEnum.SINGLE,
      runConf: '',
      priority,
      globalParams
    }
  });
}

/** 删除 */
export function deleteJob(workspaceId?: string, jobId?: string): BaseResponseType<{jobId: string}> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}`,
    method: 'DELETE'
  });
}

/** 获取 调度信息 已废弃 */
export function getSchedule(workspaceId?: string, jobId?: string): BaseResponseType<ICron> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}/schedule`,
    method: 'GET'
  });
}
/** 修改调度信息 已废弃 */
export function updateSchedule(
  workspaceId: string,
  jobId: string,
  data: ICron
): BaseResponseType<{jobId: number}> {
  return editJob({workspaceId, jobId, scheduleConf: data});
}

/** 切换调度状态 */
export function switchSchedule(
  workspaceId: string,
  jobId: string,
  scheduleStatus: JobScheduleStatusEnum
): BaseResponseType<ICron> {
  const type = JobScheduleStatusEnum.ON === scheduleStatus ? 'enable' : 'disable';
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}/schedule/${type}`,
    method: 'PATCH'
  });
}

// 获取全局参数 已废弃
export function getGlobalParams(workspaceId?: string, jobId?: string): BaseResponseType<IKeyValue[]> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}/globalparams`,
    method: 'GET'
  });
}

/** 更新全局参数 */
export function updateGlobalParams(
  workspaceId: string,
  jobId: string,
  data: IKeyValue[]
): BaseResponseType<boolean> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/${jobId}/globalparams`,
    method: 'POST',
    data: {
      globalParams: data
    }
  });
}

/** 重命名
 * @param name 名称 重命名 名称
 * @param type 类型 需要替换添加的类型 类如 添加 名称-type-时间戳
 * @param splitStr 分隔符 重命名 分隔符 -或者 _
 * @param maxLength 最大长度 重命名 最大长度 避免长度超出
 *
 */
const renameJobName = ({
  name = '',
  type = '',
  splitStr = '-',
  format = 'YYYYMMDD-HHmmss',
  maxLength = 256
}: {
  name?: string;
  type?: string;
  splitStr?: string;
  format?: string;
  maxLength?: number;
}) => {
  let newName = name;

  // 判断是否存在时间戳 存在则删除
  const timeStr = name.slice(format.length * -1);
  if (moment(timeStr, format, true).isValid()) {
    newName = name.slice(0, format.length * -1);
    const endStr = newName.endsWith(splitStr);
    // 删除最后一个分隔符
    if (endStr) {
      newName = newName.slice(0, splitStr.length * -1);
    }
  }
  const typeStr = type ? `${splitStr}${type}` : '';
  // 时间长度
  const timeLength = format.length + splitStr.length;
  // 类型长度
  const typeLength = typeStr.length;
  // 最大长度
  const max = maxLength - timeLength - typeLength;
  // 判断是否超出最大长度
  if (newName.length >= max) {
    newName = newName.slice(0, max);
  }
  newName = `${newName}${typeStr}${splitStr}${moment().format(format)}`;
  return newName;
};

/**
 * 元数据管理 - 接口集合
 */

import {OperatorFieldEnum} from '@pages/JobWorkflow/constants';
import {request, BaseResponseType, urlPrefix} from './apiFunction';
import {handleDownLoad} from '@utils/handleDownLoad';
import axios, {AxiosProgressEvent} from 'axios';
import qs from 'qs';
import {Request_Method} from './common';
import {Privilege} from '@api/permission/type';

type ApiResPromise<T> = BaseResponseType<T>;

export interface ISearchMetastoreParams {
  /**
   * 名称过滤条件
   */
  filter?: null | string;
  /**
   * 当前工作空间 id
   */
  workspaceId: string;
}

export interface ISearchMetastoreCatalog {
  name: string;
  type: CatalogType;
  schemas: Array<{
    name: string;
    operators: Array<{name: string; [property: string]: any}>;
    tables: Array<{name: string; [property: string]: any}>;
    volumes: Array<{name: string; [property: string]: any}>;
    datasets: Array<{name: string; [property: string]: any}>;
    models: Array<{name: string; [property: string]: any}>;
    [property: string]: any;
  }>;
}

export interface ISearchMetastoreRes {
  catalogs: Array<ISearchMetastoreCatalog>;
  unassignedCatalogs: Array<ISearchMetastoreCatalog>;
}

// 元数据 Tree 实体搜索
// ? workspaceId有问题
export function searchMetastore(
  workspaceId: string,
  params: ISearchMetastoreParams
): ApiResPromise<ISearchMetastoreRes> {
  return request({
    url: `${urlPrefix}/metastore/search`,
    method: 'POST',
    data: params,
    params: {
      workspaceId
    }
  });
}

/*****************  Catalog 相关接口  *****************/

export interface ICatalogRes {
  catalogs: Array<{name: string; type: CatalogType}>;
  unassignedCatalogs: Array<{name: string; type: CatalogType}>;
}

// 获取 Catalog 列表
// ? workspaceId有问题
export function getCatalogList(workspaceId: string): ApiResPromise<ICatalogRes> {
  return request({
    url: `${urlPrefix}/catalogs`,
    method: 'GET',
    params: {workspaceId},
    transformResponse: [
      ...(typeof axios.defaults.transformResponse === 'function'
        ? [axios.defaults.transformResponse]
        : axios.defaults.transformResponse),
      (data) => {
        const sort = (a, b) => {
          // 首先将name='system'的项排在最前面
          if (a.name === CatalogType.SYSTEM && b.name !== CatalogType.SYSTEM) return -1;
          if (a.name !== CatalogType.SYSTEM && b.name === CatalogType.SYSTEM) return 1;
          // 然后将type=CatalogType.DORIS的项排在前面
          if (a.type === CatalogType.DORIS && b.type !== CatalogType.DORIS) return -1;
          if (a.type !== CatalogType.DORIS && b.type === CatalogType.DORIS) return 1;
        };
        if (data.result?.catalogs.length > 0) {
          data.result = {
            catalogs: data.result.catalogs.sort(sort),
            unassignedCatalogs: data.result.unassignedCatalogs.sort(sort)
          };
        }
        return data;
      }
    ]
  });
}

export interface ICatalogParams {
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * workspaceId 工作空间命名
   */
  workspaceId: string;
  /**
   * catalog 名称
   */
  name?: string;
}

// 创建 Catalog
// * 通了
export function createCatalog(
  workspaceId: string,
  params: Omit<ICatalogParams, 'workspaceId'>
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/catalogs`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

// 删除 Catalog
// * 通了 功能正常
export function deleteCatalog(workspaceId: string, fullName: string): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/catalogs/${fullName}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}

// 修改 Catalog
// * 通了 功能正常
export function patchCatalog(
  workspaceId: string,
  fullName: string,
  params: Omit<ICatalogParams, 'workspaceId'>
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/catalogs/${fullName}`,
    method: 'PATCH',
    data: params,
    params: {workspaceId}
  });
}

// 数据目录 类型
export enum CatalogType {
  // 特殊的 Catalog（内置）
  SYSTEM = 'system',
  // 特殊的 Catalog（内置）
  EDAP_DATALAKE = 'EDAPDataLake',
  // 普通元数据
  DATALAKE = 'Datalake',
  // 查询检索实例
  DORIS = 'Doris'
}

/**
 * catalog 属性
 */
export interface CatalogProperties {
  /**
   * 查询检索实例ID。Doris 类型拥有
   */
  computeId?: string;
  /**
   * 查询检索实例名称。Doris 类型拥有
   */
  computeName?: string;
  [property: string]: string;
}

export interface ICatalogDetailRes {
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * catalog 唯一标识
   */
  id: string;
  /**
   * catalog 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: CatalogProperties;
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   *  catalog 类型
   */
  type: CatalogType;

  /**
   * 权限
   */
  privileges?: Privilege[];
}

export interface ICatalogSummaryRes {
  /**
   * operator 数
   */
  operatorCount: number;
  /**
   * table 数
   */
  tableCount: number;
  /**
   * volume 数
   */
  volumeCount: number;
  /**
   * dataset 数
   */
  datasetCount: number;
  /**
   * model 数
   */
  modelCount: number;
}

// Catalog 详情
// * 通了
export function getCatalogDetail(
  workspaceId: string,
  fullName: string,
  silent = false
): ApiResPromise<{
  catalog: ICatalogDetailRes;
  metadataSummary: ICatalogSummaryRes;
}> {
  return request({
    url: `${urlPrefix}/catalogs/${fullName}/detail`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

export interface ICatalogWorkspace {
  /** 工作空间 ID */
  workspaceId: string;
  /** 工作空间名称 */
  workspaceName: string;
}

export interface IQueryCatalogWorkspaceListParams {
  /** 当前页码 */
  pageNo?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 搜索条件：工作空间名称 */
  workspaceName?: string;
}

// 获取 Catalog下的工作空间列表
// * 通了
export function getCatalogWorkspaceList(
  workspaceId: string,
  catalogId: string,
  params: IQueryCatalogWorkspaceListParams
): ApiResPromise<{
  items: Array<ICatalogWorkspace>;
  total: number;
}> {
  return request({
    url: `${urlPrefix}/catalogs/${catalogId}/assigns`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}

// 解绑 Catalog下的工作空间
// ! 后端没测完
export function deleteCatalogWorkspace(
  workspaceId: string,
  catalogId: string,
  params: {items: string[]}
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/catalogs/${catalogId}/assign`,
    method: 'DELETE',
    data: params,
    params: {workspaceId}
  });
}

// 绑定 Catalog下的工作空间
// ! 后端没测完
export function assignCatalogWorkspace(
  workspaceId: string,
  catalogId: string,
  params: {items: ICatalogWorkspace[]}
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/catalogs/${catalogId}/assign`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}
/*****************  Catalog 结束  *****************/

/*****************  Scheme 相关接口  *****************/

export interface ISchemaListParams {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
  // 权限过滤
  privileges?: string[];
}

// 获取 Scheme 列表
// ? workspaceId有问题
export function getSchemaList(
  workspaceId: string,
  params: Omit<ISchemaListParams, 'workspaceId'>
): ApiResPromise<{schemas: string[]}> {
  return request({
    url: `${urlPrefix}/schemas`,
    method: 'GET',
    params: {...params, workspaceId},
    // 使用逗号分隔数组
    paramsSerializer: (params) => qs.stringify(params, {arrayFormat: 'comma'})
  });
}

export interface ISchemaParams {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * schema 名
   */
  name: string;
}

// 创建 Schema
// * 通了
export function createSchema(workspaceId: string, params: ISchemaParams): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/schemas`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

// 删除 Schema
// * 通了
export function deleteSchema(workspaceId: string, fullName: string): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/schemas/${fullName}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}

// 修改 Schema
// * 通了
export function patchSchema(
  workspaceId: string,
  fullName: string,
  params: Pick<ISchemaParams, 'name'> | Pick<ISchemaParams, 'comment'>
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/schemas/${fullName}`,
    method: 'PATCH',
    data: params,
    params: {workspaceId}
  });
}

export interface ISchemaSummaryRes {
  /**
   * operator 数
   */
  operatorCount: number;
  /**
   * table 数
   */
  tableCount: number;
  /**
   * volume 数
   */
  volumeCount: number;
  /**
   * dataset 数
   */
  datasetCount: number;
  /**
   * model 数
   */
  modelCount: number;
}

export interface ISchemaDetailRes {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * schema 唯一标识
   */
  id: string;
  /**
   * schema 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;

  /**
   * 权限
   */
  privileges?: Privilege[];
}

// Schema 详情
// * 通了
export function getSchemaDetail(
  workspaceId: string,
  fullName: string,
  silent = false
): ApiResPromise<{
  metadataSummary: ISchemaSummaryRes;
  schema: ISchemaDetailRes;
}> {
  return request({
    url: `${urlPrefix}/schemas/${fullName}/detail`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

/*****************  Scheme 结束  *****************/

/*****************  Table 相关接口  *****************/

export interface ITableListParams {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
  /**
   * schema 名称
   */
  schemaName: string;
  // 权限
  privileges?: Privilege[];
}

// 获取 Table 列表
// ! 后端没测完
export function getTableList(
  workspaceId: string,
  params: Omit<ITableListParams, 'workspaceId'>
): ApiResPromise<{tables: string[]}> {
  return request({
    url: `${urlPrefix}/tables`,
    method: 'GET',
    params: {...params, workspaceId},
    // 使用逗号分隔数组
    paramsSerializer: (params) => qs.stringify(params, {arrayFormat: 'comma'})
  });
}

export interface ITableColumn {
  /**
   * 列的描述
   */
  comment?: string;
  /**
   * 列名
   */
  name: string;
  /**
   * 是否非空
   */
  nullable: boolean;
  /**
   * 分区列的序数位置。从 0 开始
   */
  partitionIndex: number;
  /**
   * 列的序数位置，从 0 开始
   */
  position: number;
  /**
   * IntervalType 的格式
   */
  typeIntervalType?: null | string;
  /**
   * JSON 格式的数据类型描述
   */
  typeJson?: null | string;
  /**
   * 列类型
   */
  typeName: ITableTypeName;
  /**
   * DecimalType 的精度
   */
  typePrecision?: number | null;
  /**
   * DecimalType 的标度，即小数部分的位数
   */
  typeScale?: number | null;
  /**
   * SQL形式的数据类型描述
   */
  typeText?: null | string;
}
export interface ITableParams {
  /**
   * 存储访问 ak。EXTERNAL 必须
   */
  accessKeyId?: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 列
   */
  columns: ITableColumn[];
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 数据源格式
   */
  dataSourceFormat: ITableDataSourceFormat;
  /**
   * table 名
   */
  name: string;
  properties?: {[key: string]: any};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储访问 sk。EXTERNAL 必须
   */
  secretAccessKey?: string;
  /**
   * EXTERNAL 必须
   */
  storageLocation?: string;
  /**
   * 表类型
   */
  tableType: 'EXTERNAL' | 'MANAGED';
}

export enum ITableTypeName {
  Array = 'ARRAY',
  Binary = 'BINARY',
  Boolean = 'BOOLEAN',
  Byte = 'BYTE',
  Char = 'CHAR',
  Date = 'DATE',
  Decimal = 'DECIMAL',
  Double = 'DOUBLE',
  Float = 'FLOAT',
  Int = 'INT',
  Interval = 'INTERVAL',
  Long = 'LONG',
  Map = 'MAP',
  Null = 'NULL',
  Short = 'SHORT',
  String = 'STRING',
  Struct = 'STRUCT',
  TableType = 'TABLE_TYPE',
  Timestamp = 'TIMESTAMP',
  TimestampNtz = 'TIMESTAMP_NTZ',
  UserDefineType = 'USER_DEFINE_TYPE'
}

export enum ITableDataSourceFormat { // table 数据源格式
  Avro = 'AVRO',
  Csv = 'CSV',
  Iceberg = 'ICEBERG',
  Json = 'JSON',
  Orc = 'ORC',
  Parquet = 'PARQUET',
  Text = 'TEXT'
}

// 创建 Table
// 本期不涉及
export function createTable(workspaceId: string, params: any): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/tables`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

// 删除 Table
// * 通
export function deleteTable(workspaceId: string, fullName: string): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/tables/${fullName}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}

// 修改 Table
// *
export function patchTable(
  workspaceId: string,
  fullName: string,
  params: Pick<ITableParams, 'name'> | Pick<ITableParams, 'comment'>
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/tables/${fullName}`,
    method: 'PATCH',
    data: params,
    params: {workspaceId}
  });
}

export interface ITableDetailRes {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 列
   */
  columns: ITableColumn[];
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 数据源格式
   */
  dataSourceFormat: ITableDataSourceFormat;
  /**
   * table 唯一标识
   */
  id: string;
  /**
   * table 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 表类型
   */
  tableType: 'EXTERNAL' | 'MANAGED';
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;

  privileges: Privilege[];
}

// Table 详情
// * 通了
export function getTableDetail(
  workspaceId: string,
  fullName: string,
  silent = false
): ApiResPromise<ITableDetailRes> {
  return request({
    url: `${urlPrefix}/tables/${fullName}`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

/*****************  Table 结束  *****************/

/*****************  Volume 相关接口  *****************/

export interface IVolumeListParams {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
  /**
   * schema 名称
   */
  schemaName: string;
}

// 获取 Volume 列表
// ? workspaceId影响
export function getVolumeList(
  workspaceId: string,
  params: Omit<IVolumeListParams, 'workspaceId'>,
  privileges?: Privilege
): ApiResPromise<{volumes: string[]}> {
  return request({
    url: `${urlPrefix}/volumes`,
    method: 'GET',
    params: {...params, workspaceId, privileges}
  });
}

// volume 类型
export enum EVolumeType {
  EXTERNAL = 'EXTERNAL',
  MANAGED = 'MANAGED'
}

export interface IVolumeParams {
  /**
   * 存储访问 ak。EXTERNAL 必须
   */
  accessKeyId?: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * volume 名
   */
  name: string;
  properties?: {[key: string]: any};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储访问 sk。EXTERNAL 必须
   */
  secretAccessKey?: string;
  /**
   * 存储路径。EXTERNAL 必须
   */
  storageLocation?: string;
  /**
   * volume 类型
   */
  volumeType: EVolumeType;
}

// 创建 Volume
// * 通了
export function createVolume(workspaceId: string, params: IVolumeParams): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/volumes`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

// 删除 Volume
// * 通了
export function deleteVolume(workspaceId: string, fullName: string): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/volumes/${fullName}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}

// 修改 Volume
// * 通了
export function patchVolume(
  workspaceId: string,
  fullName: string,
  params: Pick<IVolumeParams, 'comment'> | Pick<IVolumeParams, 'name'>
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/volumes/${fullName}`,
    method: 'PATCH',
    data: params,
    params: {workspaceId}
  });
}

export interface IVolumeDetailRes {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * volume 唯一标识
   */
  id: string;
  /**
   * volume 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * volume 类型
   */
  volumeType: 'EXTERNAL' | 'MANAGED';
  /**
   * 权限列表
   */
  privileges: Privilege[];
}

// Volume 详情
// * 通了
export function getVolumeDetail(
  workspaceId: string,
  fullName: string,
  silent = false
): ApiResPromise<IVolumeDetailRes> {
  return request({
    url: `${urlPrefix}/volumes/${fullName}`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

/*****************  Volume 结束  *****************/
/*****************  Volume 文件管理接口  *****************/

export interface IVolumeFileListQuerys {
  /**
   * 当前页从 marker 指向的下一个文件开始。默认为""
   */
  marker?: string;
  /**
   * 页大小。默认30，最大200。
   */
  maxSize?: number;
  /**
   * volume虚拟路径。形如 {catalogName}/{schemaName}/{volumeName}/path/to/your/file
   */
  path: string;
}

export interface IVolumeListRes {
  /**
   * 文件或文件夹列表
   */
  files: IVolumeListFile[];
  /**
   * 查询本页marker的值
   */
  marker: string;
  /**
   * 查询下页时marker的值
   */
  nextMarker: string;
  /**
   * 当前目录虚拟路径
   */
  path: string;
  /**
   * 本次是否已返回全部结果
   */
  truncated: boolean;
  /**
   * 权限列表
   */
  privileges: Privilege[];
}

export interface IVolumeListFile {
  /**
   * 文件或文件夹名。以"/"结尾的为文件夹
   */
  name: string;
  /**
   * 当前文件虚拟路径
   */
  path: string;
  /**
   * 文件字节大小
   */
  size: number;
  /**
   * 最近修改时间
   */
  updatedAt: Date;
}

// 获取 Volume 文件列表
// ? 修改volume会不可访问 后端bug
export function getVolumeFileList(
  workspaceId: string,
  fullName: string,
  params: IVolumeFileListQuerys
): ApiResPromise<IVolumeListRes> {
  return request({
    url: `${urlPrefix}/volumes/${fullName}/files`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}

export interface IUploadVolumeFileParams extends FormData {
  file: any; // application/form-data 的文件
}

// Volume 文件上传
// * 通了
export function uploadVolumeFile(
  workspaceId: string,
  fullName: string,
  data: IUploadVolumeFileParams,
  path: string,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void,
  controller?: AbortController
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/volumes/${fullName}/files`,
    method: 'POST',
    data: data,
    params: {path, workspaceId},
    onUploadProgress,
    signal: controller.signal
  });
}

// Volume 文件删除
// * 通
export function deleteVolumeFile(
  workspaceId: string,
  fullName: string,
  params: {files: string[]}
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/volumes/${fullName}/files`,
    method: 'DELETE',
    data: params,
    params: {workspaceId},
    paramsSerializer: (params) => qs.stringify(params, {arrayFormat: 'comma'})
  });
}

// Volume 文件下载
// * 通
export function downloadVolumeFile(workspaceId: string, fullName: string, params: {file: string}): void {
  window.open(
    `${urlPrefix}/volumes/${fullName}/files/download?workspaceId=${workspaceId}&file=${encodeURIComponent(params.file)}`
  );
}

/*****************  Volume 文件管理结束  *****************/

/*****************  Operator  相关接口  *****************/

export interface IOperatorListQuerys {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
  /**
   * schema 名称
   */
  schemaName: string;
}

export interface IOperatorParams {
  /**
   * 别名，显示名称
   */
  alias?: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * operator 名
   */
  name: string;
  properties?: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 使用说明
   */
  usage?: null | string;
}

// 获取 Operator 列表
// * 通
export function getOperatorList(
  workspaceId: string,
  params: IOperatorListQuerys
): ApiResPromise<{operators: string[]}> {
  return request({
    url: `${urlPrefix}/operators`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}

// 创建 Operator
// 本期没有
export function createOperator(workspaceId: string, params: IOperatorParams): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/operators`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

export interface IOperatorPathcParams {
  /**
   * 新别名
   */
  alias?: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 新 operator 名
   */
  name?: string;
  /**
   * 使用说明
   */
  usage?: null | string;
}

// 修改 Operator
// 本期没有
export function patchOperator(
  workspaceId: string,
  fullName: string,
  params: IOperatorPathcParams
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/operators/${fullName}`,
    method: 'PATCH',
    data: params,
    params: {workspaceId}
  });
}

export interface IOperatorDetailRes {
  /**
   * operator 别名，显示名称
   */
  alias: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * operator 唯一标识
   */
  id: string;
  /**
   * 最新版本 ID
   */
  latestVersionId: string;
  /**
   * 最新版本号
   */
  latestVersionName: string;
  /**
   * operator 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * 使用说明
   */
  usage?: null | string;

  privileges: Privilege[];
}

// 查询 Operator 详情
// * 通
export function getOperatorDetail(
  workspaceId: string,
  fullName: string,
  silent?: boolean
): ApiResPromise<IOperatorDetailRes> {
  return request({
    url: `${urlPrefix}/operators/${fullName}`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

/**
 * 算子类型
 */
export enum IOperCategoryEnum {
  Dedup = 'DEDUP',
  Embedding = 'EMBEDDING',
  Extract = 'EXTRACT',
  Filter = 'FILTER',
  Others = 'OTHERS',
  Transform = 'TRANSFORM',
  Source = 'SOURCE',
  Sink = 'SINK'
}

export interface IOperParameterSchema {
  /**
   * 字段描述
   */
  comment?: string;
  /**
   * 默认值
   */
  defaultValue?: string;
  /**
   * 字段名
   */
  name: string;
  /**
   * 是否必须
   */
  required?: boolean;
  /**
   * 字段类型
   */
  type: string;
}

export interface IOperInputOutput {
  /**
   * 字段描述
   */
  comment?: string;
  /**
   * 字段名
   */
  name: string;
  /**
   * 是否必须
   */
  required?: boolean;
  /**
   * 字段类型
   */
  type: string;
  /**
   * 默认值
   */
  defaultValue?: string;
}

export interface IOperVersionOneRes {
  /**
   * 算子类型
   */
  category: IOperCategoryEnum;
  /**
   * 算子过滤类型
   */
  field: OperatorFieldEnum;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;

  /**
   * 算子别名
   */
  operatorAlias?: string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 引擎类型
   */
  engineType: Array<'RAY' | 'SPARK'>;
  /**
   * 运行参数
   */
  execParams: IOperParameterSchema[];
  /**
   * version 唯一标识
   */
  id: string;
  /**
   * 输入参数
   */
  input: IOperInputOutput[];
  /**
   * 代码语言
   */
  language: string;
  /**
   * version 名
   */
  name: string;
  /**
   * operator 名
   */
  operatorName: string;
  /**
   * 输出参数
   */
  output: IOperInputOutput[];
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * 资源类型
   */
  resourceType: 'CPU' | 'GPU';
  /**
   * 运行环境
   */
  runtimeEnv: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 算子代码路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  // 版本
  version?: string;
}

// 获取 Operator 某一个算子版本详情
// ? 暂时不需要
export function getOperatorOneDetail(
  workspaceId: string,
  fullName: string,
  versionName: string
): ApiResPromise<IOperVersionOneRes> {
  return request({
    url: `${urlPrefix}/operators/${fullName}/versions/${versionName}`,
    method: 'GET',
    params: {workspaceId}
  });
}

export interface IOperAllVersionRes {
  /**
   * 版本总数
   */
  total: number;
  /**
   * Operator 版本列表
   */
  versions: IOperVersionOneRes[];
}

// 获取 Operator 算子版本列表
// * 通
export function getOperatorAllList(
  workspaceId: string,
  fullName: string,
  params: {
    /**
     * 页号。从 1 开始，默认值为1
     */
    pageNo?: number;
    /**
     * 页大小。默认10，最大100。
     */
    pageSize?: number;
  }
): ApiResPromise<IOperAllVersionRes> {
  return request({
    url: `${urlPrefix}/operators/${fullName}/versions`,
    method: 'GET',
    params: {...params, workspaceId}
  }).then((res: any) => {
    return {
      ...res,
      result: {
        ...res.result,
        versions: res.result?.versions?.map((item: any) => {
          return {
            ...item,
            privileges: res.result.privileges
          };
        })
      }
    };
  });
}
// 算子最新版本列表。当前只返回系统内置算子,工作流列表展示
export function getOperatorLatestList(workspaceId: string): ApiResPromise<{versions: IOperVersionOneRes[]}> {
  return request({
    url: `${urlPrefix}/operators/latest-versions`,
    method: 'GET',
    params: {workspaceId}
  });
}

// 新增 Operator 算子版本  (一期不支持)

// 删除 Operator 算子版本 (一期不支持)

// 修改 Operator 算子版本 (一期不支持)

/*****************  Operator  结束  *****************/

/*****************  model&dataset 开始  *****************/
export const EnumMetaType = {
  VOLUME: 'volumes',
  DATASETS: 'datasets',
  MODELS: 'models',
  TABLE: 'tables'
};
export interface IDatasetParams {
  /**
   * dataset 名
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * dataset 类型（复用volume）
   */
  datasetType: EVolumeType;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 数据类型：枚举值 TEXT、IMAGE、AUDIO、VIDEO、GENERAL
   */
  dataType: string;
  /**
   * 存储类型：BOS、PFS。EXTERNAL 必须。
   */
  storageType: string;
}
// 创建 dataset
export function createDataset(workspaceId: string, params: IDatasetParams): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/datasets`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

export interface IModelParams {
  /**
   * dataset 名
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * dataset 类型（复用volume）
   */
  datasetType: EVolumeType;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 数据类型：枚举值 TEXT、IMAGE、AUDIO、VIDEO、GENERAL
   */
  dataType: string;
  /**
   * 存储类型：BOS、PFS。EXTERNAL 必须。
   */
  storageType: string;
}
// 创建 model
export function createModel(workspaceId: string, params: IModelParams): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/models`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

export interface IDatasetDetailRes {
  /**
   * dataset 唯一标识
   */
  id: string;
  /**
   * dataset 名
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * dataset 类型
   */
  datasetType: 'EXTERNAL' | 'MANAGED';
  /**
   * 数据类型
   */
  dataType?: 'TEXT' | 'IMAGE' | 'AUDIO' | 'VIDEO' | 'GENERAL';
  /**
   * 存储类型
   */
  storageType?: 'BOS' | 'PFS' | 'HDFS'; // 公有云 BOS，私有化 HDFS
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最新版本 ID
   */
  latestVersionId: string;
  /**
   * 最新版本号
   */
  latestVersionName: string;
  /**
   * 权限列表
   */
  privileges: Privilege[];
  properties: {[key: string]: string};
}
// dataset 详情
export function getDatasetDetail(
  workspaceId: string,
  fullName: string,
  silent = false
): ApiResPromise<IDatasetDetailRes> {
  return request({
    url: `${urlPrefix}/datasets/${fullName}`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

export interface IModelDetailRes {
  /**
   * model 唯一标识
   */
  id: string;
  /**
   * model 名
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * model 类型
   */
  modelType: 'EXTERNAL' | 'MANAGED';
  /**
   * 数据类型
   */
  dataType?: 'TEXT' | 'IMAGE' | 'AUDIO' | 'VIDEO' | 'GENERAL';
  /**
   * 存储类型
   */
  storageType?: 'BOS' | 'PFS' | 'HDFS'; // 公有云 BOS，私有化 HDFS
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最新版本 ID
   */
  latestVersionId: string;
  /**
   * 最新版本号
   */
  latestVersionName: string;
  /**
   * 权限列表
   */
  privileges: Privilege[];
  properties: {[key: string]: string};
}
// model 详情
export function getModelDetail(
  workspaceId: string,
  fullName: string,
  silent = false
): ApiResPromise<IModelDetailRes> {
  return request({
    url: `${urlPrefix}/models/${fullName}`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

export interface IDatasetOrModelListParams {
  /**
   * metastore id
   */
  workspaceId: string;
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * schema 名称
   */
  schemaName: string;
}
// 获取 model&dataset 名字列表
export function getDatasetOrModelList(
  workspaceId: string,
  metaType: string = 'datasets',
  params: IDatasetOrModelListParams
): ApiResPromise<{datasets?: string[]; models?: string[]}> {
  return request({
    url: `${urlPrefix}/${metaType}`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}
// 修改 model&dataset
export function patchDatasetOrModel(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string,
  params:
    | Pick<IDatasetParams, 'comment'>
    | Pick<IModelParams, 'comment'>
    | Pick<IDatasetParams, 'name'>
    | Pick<IModelParams, 'name'>
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/${metaType}/${fullName}`,
    method: 'PATCH',
    data: params,
    params: {workspaceId}
  });
}
// 删除 model&dataset
export function deleteDatasetOrModel(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/${metaType}/${fullName}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}
/*****************  model&dataset 结束  *****************/

/*****************  model&dataset version 开始  *****************/
export interface IDatasetVersionParams {
  /**
   * dataset 名
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 属性
   */
  properties?: {[key: string]: any};
  /**
   * 存储路径。EXTERNAL 必须
   */
  storageLocation?: string;
  /**
   * 存储访问 ak。EXTERNAL 必须
   */
  accessKeyId?: string;
  /**
   * 存储访问 sk。EXTERNAL 必须
   */
  secretAccessKey?: string;
}

// 创建 dataset version
export function createDataseVersion(workspaceId: string, params: IDatasetVersionParams): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/datasets/versions`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}

export interface IModelVersionParams {
  /**
   * 模型名
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 模型格式
   */
  baseModel: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 属性
   */
  properties?: {[key: string]: any};
  /**
   * 存储路径。EXTERNAL 必须
   */
  storageLocation?: string;
  /**
   * 存储访问 ak。EXTERNAL 必须
   */
  accessKeyId?: string;
  /**
   * 存储访问 sk。EXTERNAL 必须
   */
  secretAccessKey?: string;
}
// 创建 model version
export function createModelVersion(workspaceId: string, params: IModelVersionParams): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/models/versions`,
    method: 'POST',
    data: params,
    params: {workspaceId}
  });
}
export interface IDatasetVersionDetailRes {
  /**
   * dataset version 唯一标识
   */
  id: string;
  /**
   * version 版本号
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * dataset 名
   */
  datasetName: string;
  /**
   * 数据格式
   */
  dataFormat: string[];
  /**
   * 描述
   */
  comment: null | string;
  /**
   * 样本数
   */
  sampleCount: number;
  /**
   * 数据集大小
   */
  datasetSize: number;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * 数据集存储或挂载路径
   */
  storageLocation: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * 权限列表
   */
  privileges: Privilege[];
}

// dataset version 详情
export function getDatasetVersionDetail(
  workspaceId: string,
  fullName: string,
  versionName: string,
  silent = false
): ApiResPromise<IDatasetVersionDetailRes> {
  return request({
    url: `${urlPrefix}/datasets/${fullName}/versions/${versionName}`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

export interface IModelVersionDetailRes {
  /**
   * dataset version 唯一标识
   */
  id: string;
  /**
   * version 版本号
   */
  name: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * model 名
   */
  modelName: string;
  /**
   * model 基础模型
   */
  baseModel: string;
  /**
   * 描述
   */
  comment: null | string;
  /**
   * 数据集大小
   */
  modelSize: number;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * 存储或挂载路径
   */
  storageLocation: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * 权限列表
   */
  privileges: Privilege[];
}

// model version 详情
export function getModelVersionDetail(
  workspaceId: string,
  fullName: string,
  versionName: string,
  silent = false
): ApiResPromise<IModelVersionDetailRes> {
  return request({
    url: `${urlPrefix}/models/${fullName}/versions/${versionName}`,
    method: 'GET',
    silent,
    params: {workspaceId}
  });
}

// 修改 model&dataset 版本信息内容
export function patchDatasetOrModelVersionDetail(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string,
  versionName: string,
  params: Pick<IDatasetVersionParams, 'comment'> | Pick<IModelVersionParams, 'comment'>
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/${metaType}/${fullName}/versions/${versionName}`,
    method: 'PATCH',
    data: params,
    params: {workspaceId}
  });
}

// 删除 model&dataset version
export function deleteDatasetOrModelVersion(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string,
  versionName: string
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/${metaType}/${fullName}/versions/${versionName}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}
// 获取 dataset version 列表
export function getDatasetVersionList(
  workspaceId: string,
  fullName: string,
  params: {
    /**
     * 页号。从 1 开始，默认值为1
     */
    pageNo?: number;
    /**
     * 页大小。默认10，最大100。
     */
    pageSize?: number;
  }
): ApiResPromise<{versions: IDatasetVersionDetailRes[]; total: number}> {
  return request({
    url: `${urlPrefix}/datasets/${fullName}/versions`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}

// 获取 model version 列表
export function getModelVersionList(
  workspaceId: string,
  fullName: string,
  params: {
    /**
     * 页号。从 1 开始，默认值为1
     */
    pageNo?: number;
    /**
     * 页大小。默认10，最大100。
     */
    pageSize?: number;
  }
): ApiResPromise<{versions: IModelVersionDetailRes[]; total: number}> {
  return request({
    url: `${urlPrefix}/models/${fullName}/versions`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}

export enum PreviewStatus {
  PROCESSING = 'PROCESSING',
  READY = 'READY',
  FAILED = 'FAILED',
  NODATA = 'NODATA'
}
export interface IDatasetVersionPreivewDataRes {
  // 预览状态，状态枚举：PROCESSING、READY、FAILED
  previewStatus: string;
  // 数据集状态状态为 FAILED 和 PROCESSING 时，展示预处理失败的原因
  errorMessage: string;
  features: {
    featureIdx: number;
    name: string;
  }[];
  rows: {
    // 行号
    rowIdx: number;
    // 行数据
    row: object;
  }[];
  // 数据总数
  previewRowsTotal: number;
  // 分页条数: 每页可看的数据行数
  numRowsPerPage: number;
}
// 获取 dataset version 的预览数据
export function getDatasetVersionDataRreview(
  workspaceId: string,
  fullName: string,
  versionName: string,
  params: {
    /**
     * 页号。从 1 开始，默认值为1
     */
    pageNo?: number;
    /**
     * 页大小。100
     */
    pageSize?: number;
  }
): ApiResPromise<IDatasetVersionPreivewDataRes> {
  return request({
    url: `${urlPrefix}/datasets/${fullName}/versions/${versionName}/preview`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}

/*****************  model&dataset version 结束  *****************/

/***************** [common] the file of version about dataset and model  *****************/
export interface IDatasetAndModelOfVerionFileListQuerys {
  /**
   * 当前页从 marker 指向的下一个文件开始。默认为""
   */
  marker?: string;
  /**
   * 页大小。默认30，最大200。
   */
  maxSize?: number;
  /**
   * 虚拟路径。形如 {catalogName}/{schemaName}/{datasetName | modelName}/{versionName}/path/to/your/file
   */
  path: string;
}
export interface IDatasetAndModelVersionListFile {
  /**
   * 文件或文件夹名。以"/"结尾的为文件夹
   */
  name: string;
  /**
   * 当前文件虚拟路径
   */
  path: string;
  /**
   * 文件字节大小
   */
  size: number;
  /**
   * 最近修改时间
   */
  updatedAt: Date;
}
/***************** [common] the file of version about dataset and model  *****************/

/*****************  model&dataset version files 开始  *****************/
export interface IDatasetAndModelVersionListBaseRes {
  /**
   * 查询本页marker的值
   */
  marker: string;
  /**
   * 查询下页时marker的值
   */
  nextMarker: string;
  /**
   * 当前目录虚拟路径
   */
  path: string;
  /**
   * 本次是否已返回全部结果
   */
  truncated: boolean;
}
export interface IDatasetAndModelVersionListRes extends IDatasetAndModelVersionListBaseRes {
  /**
   * 文件或文件夹列表
   */
  files: IDatasetAndModelVersionListFile[];
}
// 获取 model&dataset version 文件列表
export function getDatasetOrModelVersionFileList(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string,
  versionName: string,
  params: IDatasetAndModelOfVerionFileListQuerys
): ApiResPromise<IDatasetAndModelVersionListRes> {
  return request({
    url: `${urlPrefix}/${metaType}/${fullName}/versions/${versionName}/files`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}

export interface IUploadDatasetOrModelVersionFileParams extends FormData {
  file: any; // application/form-data 的文件
}
// model&dataset version 文件上传
export function uploadDatasetOrModelVersionFile(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string,
  versionName: string,
  data: IUploadDatasetOrModelVersionFileParams,
  path: string,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void,
  controller?: AbortController
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/${metaType}/${fullName}/versions/${versionName}/files`,
    method: 'POST',
    data: data,
    params: {path, workspaceId},
    onUploadProgress,
    signal: controller.signal
  });
}

// model&dataset version 文件删除
export function deleteDatasetOrModelVersionFile(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string,
  versionName: string,
  params: {files: string[]}
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/${metaType}/${fullName}/versions/${versionName}/files`,
    method: 'DELETE',
    data: params,
    params: {workspaceId},
    paramsSerializer: (params) => qs.stringify(params, {arrayFormat: 'comma'})
  });
}

// model&dataset version 文件下载
export function downloadDatasetOrModelVersionFile(
  workspaceId: string,
  metaType: string = 'datasets',
  fullName: string,
  versionName: string,
  params: {file: string}
): void {
  window.open(
    `${urlPrefix}/${metaType}/${fullName}/versions/${versionName}/files/download?workspaceId=${workspaceId}&file=${encodeURIComponent(params.file)}`
  );
}
// model&dataset version 创建文件夹
export function createDatasetOrModelVersionFileDir(
  workspaceId: string,
  metaType: string = 'datasets',
  params: {
    dirPath: string;
  }
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/${metaType}/dirs/create`,
    method: 'POST',
    data: params,
    params: {...params, workspaceId}
  });
}
export interface ValidateRes {
  /**
   * 校验结果：OK / INVALID / CATALOG_NOT_EXIST / SCHEMA_NOT_EXIST / VOLUME_NOT_EXIST / DIR_NOT_EXIST
   */
  validateResult: string;
  /**
   * msg消息
   */
  msg: string | null;
}
// model&dataset version 文件路径合法性校验
export function datasetVersionFilePathValidate(
  workspaceId: string,
  metaType: string = 'datasets',
  params: {
    path: string;
    workspaceId: string;
  }
): ApiResPromise<ValidateRes> {
  return request({
    url: `${urlPrefix}/${metaType}/path/validate`,
    method: 'GET',
    params: {...params, workspaceId}
  });
}
/*****************  model&dataset version files 结束  *****************/
enum MetaType {
  Catalog = 'CATALOG',
  Volumn = 'VOLUME',
  Schema = 'SCHEMA',
  Dataset = 'DATASET',
  Model = 'MODEL'
}

export interface MetaSearchItem {
  /**
   * 具体名字
   */
  name: string;
  parentName: string;
  /**
   * 元数据类型
   */
  type: MetaType;
}

export enum EntityType {
  CATALOG = 'CATALOG',
  SCHEMA = 'SCHEMA',
  VOLUME = 'VOLUME',
  DATASET = 'DATASET',
  TABLE = 'TABLE',
  MODEL = 'MODEL',
  OPERATOR = 'OPERATOR'
}

/**
 * 根据名称模糊搜索匹配的 Catalog/Schema/Volume
 * @param workspaceId
 * @param filter
 */
export function searchMeta(
  workspaceId: string,
  keyword: string,
  entitiesTypes: EntityType[]
): ApiResPromise<MetaSearchItem[]> {
  return request({
    url: `${urlPrefix}/metastore/fuzzysearch`,
    method: Request_Method.Post,
    // 查询接口报错，临时在 body 也添加参数，确定后可以取消一个 workspaceId
    data: {keyword, entitiesTypes, workspaceId},
    params: {workspaceId}
  });
}

/**
 * Volume 创建文件夹
 */
export function createVolumeFolder(
  fullName: string,
  dirPath: string,
  workspaceId: string
): ApiResPromise<unknown> {
  return request({
    url: `${urlPrefix}/volumes/${fullName}/dirs/create`,
    method: Request_Method.Post,
    params: {workspaceId, dirPath}
  });
}

export enum ValidateResult {
  // 路径合法且存在
  OK = 'OK',
  // 路径不合法
  INVALID = 'INVALID',
  // 合法，但catalog不存在，相关msg：xxxCatalog 不存在
  CATALOG_NOT_EXIST = 'CATALOG_NOT_EXIST',
  // 合法，但schema不存在，相关msg：xxxSchema不存在
  SCHEMA_NOT_EXIST = 'SCHEMA_NOT_EXIST',
  // 合法，但volume不存在，相关msg：xxxVolume不存在
  VOLUME_NOT_EXIST = 'VOLUME_NOT_EXIST',
  // 合法，但dir不存在，相关msg：dir1 / dir2目录不存在
  DIR_NOT_EXIST = 'DIR_NOT_EXIST',
  // 合法，但table不存在，相关msg：xxxTable不存在
  TABLE_NOT_EXIST = 'TABLE_NOT_EXIST',
  // 无权限
  ACCESS_DENIED = 'ACCESS_DENIED'
}
export interface ValidatePathResult {
  validateResult: ValidateResult;
  // msg是不存在的catalog/schema/volume/dir的名字，比如：dir1/dir2/dir3都不存在，则返回一个dir1/dir2/dir3；如果catalog不存在，则直接返回catalog名字
  msg: string;
}

// volume路径合法性校验
// 1.从左到右分级来判断路径是否存在，分别为catalog（数据目录），schema（数据模式） ，volume(数据卷），dirPath（相较于volume的存储路径）
// 2. 如果哪一级不存在，则返回相关信息，如果存在，则返回OK
export function validatePath(
  workspaceId: string,
  metaType: string = 'volumes',
  path: string
): ApiResPromise<ValidatePathResult> {
  return request({
    url: `${urlPrefix}/${metaType}/path/validate`,
    method: Request_Method.Get,
    params: {path, workspaceId}
  });
}

/**
 * 校验表路径
 * @param workspaceId 工作空间ID
 * @param path 路径
 * @returns 校验结果
 */
export function validateTablePath(workspaceId: string, path: string): ApiResPromise<ValidatePathResult> {
  return request({
    url: `${urlPrefix}/tables/validate`,
    method: Request_Method.Get,
    params: {path, workspaceId}
  });
}

/**
 * 校验schema路径 使用 table 接口校验
 * @param workspaceId 工作空间ID
 * @param path 路径
 * @returns 校验结果
 */
export function validateSchemaPath(workspaceId: string, path: string): ApiResPromise<ValidatePathResult> {
  // 校验schema路径时，需要校验schema是否存在，以及schema下是否有table
  return validateTablePath(workspaceId, path + '.table').then(
    (res: {success: boolean; status: number; result: ValidatePathResult}) => {
      if (res.success) {
        if (
          res.result.validateResult === ValidateResult.TABLE_NOT_EXIST ||
          res.result.validateResult === ValidateResult.OK
        ) {
          return {
            ...res,
            result: {
              validateResult: ValidateResult.OK,
              msg: ''
            }
          };
        }

        return res;
      }
      return res;
    }
  );
}

export interface ValidateDDLRequest {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 数据源格式
   */
  dataSourceFormat: string;
  /**
   * 建表DDL SQL
   */
  ddl: string;
  /**
   * schema 名
   */
  schemaName: string;
}

/**
 * DDL转Schema
 * @param workspaceId 工作空间ID
 * @param params 参数
 * @returns 校验结果
 */
export function validateDDL(
  workspaceId: string,
  params: ValidateDDLRequest
): ApiResPromise<{msg: string; success: boolean}> {
  return request({
    url: `${urlPrefix}/tables/ddlToSchema`,
    method: Request_Method.Post,
    params: {workspaceId},
    data: params
  });
}

export interface TableSchemaRequest {
  datasourceInfo: DatasourceInfo;
  environment: Environment;
  [property: string]: any;
}

export interface DatasourceInfo {
  /**
   * 源连接ID
   */
  connectionId: string;
  /**
   * 数据库名称
   */
  database: string;
  /**
   * 表名称
   */
  table: string;
  [property: string]: any;
}

export interface Environment {
  /**
   * 计算实例ID
   */
  computeId: string;
  /**
   * 工作空间ID
   */
  workspaceId: string;
  [property: string]: any;
}
export interface TableSchemaResult {
  /**
   * 列信息
   */
  columns: TableSchemaColumn[];
  /**
   * 表描述信息
   */
  description: string;
  /**
   * 主键字段
   */
  primaryKey: string[];
  /**
   * 外键关联关系
   */
  relations: Relation[];
  /**
   * 表名称
   */
  tableName: string;
  /**
   * 表属性
   */
  tableProperty: {[key: string]: any};
  /**
   * 表类型
   */
  tableType: string;
  /**
   * 唯一键字段
   */
  uniqueKeys: UniqueKey[];
  [property: string]: any;
}

export interface TableSchemaColumn {
  /**
   * 对应db表数据类型
   */
  dbType?: string;
  /**
   * 字段默认值
   */
  defaultValue?: string;
  /**
   * 字段描述
   */
  description?: string;
  /**
   * 是否为分区列
   */
  isPartitionColumn?: boolean;
  /**
   * 是否是主键
   */
  isPrimaryKey?: boolean;
  /**
   * 对应JDBC类型整数值
   */
  jdbcType?: number;
  keySequence?: number;
  /**
   * 字段名
   */
  name?: string;
  /**
   * 字段是否可为空
   */
  nullable?: boolean;
  /**
   * 字段精度
   */
  precision?: number;
  /**
   * 字段范围
   */
  scale?: number;
  /**
   * 字段安全等级
   */
  secretLevel?: string;
  /**
   * 字段类型
   */
  type?: string;
}

export interface Relation {
  /**
   * 外键的列名
   */
  foreignKeyColumnName?: string;
  /**
   * 包含外键的表名
   */
  foreignKeyTableName?: string;
  /**
   * 外键所引用的列名
   */
  primaryKeyColumnName?: string;
  /**
   * 外键所引用的表名
   */
  primaryKeyTableName?: string;
}

export interface UniqueKey {
  /**
   * 唯一键对应字段
   */
  fields?: string[];
  /**
   * 唯一键名称
   */
  name?: string;
}

/**
 * 查询数据源下指定表的schema
 * @returns 校验结果
 */
export function searchTableSchema(params: TableSchemaRequest): ApiResPromise<TableSchemaResult> {
  return request({
    url: `${urlPrefix}/connection/table/schema`,
    method: Request_Method.Post,
    data: params
  });
}
